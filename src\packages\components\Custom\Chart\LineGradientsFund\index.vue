<template>
  <div :style="`width:${w}px;height:${h}px;`" class="line-gradients-fund c-white">
    <!--标题-->
    <fund-head :title="props.chartConfig.option.titleValue" :type="props.chartConfig.option.tableDataType"
      :style="`width:${w}px`">
    </fund-head>
    <div class="chart-wrap" :style="`width:${w - 40}px;height:${h - 75}px;`">
      <v-chart ref="vChartRef" :init-options="initOptions" :theme="themeColor" :option="option.value"
        autoresize></v-chart>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, PropType, toRefs, onMounted } from 'vue'
import VChart from 'vue-echarts'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import { use, graphic } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import config, { includes } from './config'
import { mergeTheme } from '@/packages/public/chart'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { chartColorsSearch, defaultTheme } from '@/settings/chartThemes/index'
import { DatasetComponent, GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { useChartDataFetch } from '@/hooks'
import { isPreview, colorGradientCustomMerge } from '@/utils'
import FundHead from '@/components/FundHead/index.vue'
import { cloneDeep } from 'lodash'
import { revenueExpenditureMeal } from './data'
import {
  apiGetAnnualIncomeStatistics, apiGetAnnualProfitAnalyse,
  apiGetCanteenAnnualPerConsume, apiGetAnnualIncomeStatisticsCaiwu,
  apiBackgroundFundSupervisionBigShieldDietIncomeStatistics,
  apiBackgroundFundSupervisionBigShieldIncomeAndExpenditureStatistics
} from '@/api/path'
import { useRoute } from 'vue-router'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})

const initOptions = useCanvasInitOptions(props.chartConfig.option.chartOpts, props.themeSetting)

const route = useRoute()

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log('props.chartConfig.attr', props.chartConfig.attr);


use([DatasetComponent, CanvasRenderer, LineChart, GridComponent, TooltipComponent, LegendComponent])
const chartEditStore = useChartEditStore()

const option = reactive({
  value: props.chartConfig.option.chartOpts,
  channelId: Number(route.query.channel_id),
  orgId: Number(route.query.org_id),
  selectValue: 0
})

const SERIES_ITEM = {
  name: "",
  type: 'line',
  smooth: false,
  symbolSize: 5, //设定实心点的大小
  label: {
    show: true,
    position: 'top',
    color: '#fff',
    fontSize: 12
  },
  lineStyle: {
    width: 3,
    type: 'solid',
    color: "rgba(255, 199, 58, 1)"
  },
  areaStyle: {
    opacity: 0.5,
    color: new graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: "#FFC73A"
      },
      {
        offset: 1,
        color: 'rgba(0,0,0,0)'
      }
    ])
  },
  itemStyle: {
    color: "#FFFFFF"
  },
  data: [12572659, 12592659, 12612659, 12692659, 12652659, 12672659, 12572659, 12592659, 12612659, 12692659, 12652659, 12672659]
}

// 获取数据
const getSeriesData = async () => {
  let api
  let params: any = {}
  console.log("option.selectValue", props.chartConfig.option.tableDataType);

  if (props.chartConfig.option.tableDataType === 'revenueExpenditure') {
    api = apiGetAnnualIncomeStatistics
    params.channel_id = option.selectValue ? option.selectValue : option.channelId
    if (route.query.role_id) {
      params.role_id = Number(route.query.role_id)
    }
  } else if (props.chartConfig.option.tableDataType === 'revenueExpenditureForRoleId') {
    console.log('进入到这了')
    api = apiBackgroundFundSupervisionBigShieldIncomeAndExpenditureStatistics
    params.channel_id = option.channelId
    if (route.query.role_id) {
      params.role_id = Number(route.query.role_id)
    }
  } else if (props.chartConfig.option.tableDataType === 'yearProfit') {
    api = apiGetAnnualProfitAnalyse
    params.channel_id = option.selectValue ? option.selectValue : option.channelId
    if (route.query.role_id) {
      params.role_id = Number(route.query.role_id)
    }
  } else if (props.chartConfig.option.tableDataType === 'perCapitaConsumption') {
    api = apiGetCanteenAnnualPerConsume
    params.org_id = option.selectValue ? option.selectValue : option.orgId
  } else if (props.chartConfig.option.tableDataType === 'accountant' || props.chartConfig.option.tableDataType === 'income' || props.chartConfig.option.tableDataType === 'express') {
    api = apiGetAnnualIncomeStatisticsCaiwu
    params.channel_id = option.channelId
    params.org_list = option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : []
    // 资金膳食大屏收支统计
  } else if (props.chartConfig.option.tableDataType === 'revenueExpenditure-meal') {
    api = apiBackgroundFundSupervisionBigShieldDietIncomeStatistics
    params.channel_id = option.selectValue ? option.selectValue : option.channelId
    if (route.query.role_id) {
      params.role_id = Number(route.query.role_id)
    }
    params.org_list = option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : []
  }

  const res = await api(params)
  if (res && res.data && res.data.length) {
    let data: any = []
    let arr1: any = []
    let arr2: any = []
    if (props.chartConfig.option.tableDataType === 'revenueExpenditure' || props.chartConfig.option.tableDataType === 'revenueExpenditure-meal' || props.chartConfig.option.tableDataType === 'revenueExpenditureForRoleId') {
      res.data.map((item: any) => {
        arr1.push(item.in_price)
        arr2.push(item.out_price)
      })
      data = [arr1, arr2]
    } else if (props.chartConfig.option.tableDataType === 'yearProfit') {
      res.data.map((item: any) => {
        arr1.push(item.profit)
        arr2.push(item.last_profit)
      })
      data = [arr1, arr2]
    } else if (props.chartConfig.option.tableDataType === 'perCapitaConsumption') {
      res.data.map((item: any) => {
        arr1.push(item.average)
      })
      data = [arr1]
    } else if (props.chartConfig.option.tableDataType === 'income') {
      res.data.map((item: any) => {
        arr1.push(item.in_price)
      })
      data = [arr1]
    } else if (props.chartConfig.option.tableDataType === 'express') {
      res.data.map((item: any) => {
        arr1.push(item.out_price)
      })
      data = [arr1]
    } else if (props.chartConfig.option.tableDataType === 'accountant') {
      res.data.map((item: any) => {
        arr1.push(item.in_price)
        arr2.push(item.out_price)
      })
      data = [arr1, arr2]
    }
    initData(data)
  }
}

// 初始化数据
const initData = async (dataList: any) => {
  option.value.series = []
  if (!dataList) {
    return
  }
  dataList.forEach((dataItem: any, index: number) => {
    let seriesItem = cloneDeep(SERIES_ITEM)
    seriesItem.name = props.chartConfig.option.legendData[index]
    seriesItem.data = dataItem
    seriesItem.lineStyle.color = props.chartConfig.option.colorList[index]
    seriesItem.areaStyle.color = new graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: props.chartConfig.option.colorList[index]
      },
      {
        offset: 1,
        color: "rgba(255,255,255,0)"
      }
    ])
    option.value.series.push(seriesItem)
  })
}

// 页面加载
onMounted(() => {
  console.log("onMounted", props.chartConfig.option.tableDataType);
  initTableData()
})
// 初始化数据
const initTableData = () => {
  if (props.chartConfig.option.tableDataType === 'revenueExpenditure' ||
    props.chartConfig.option.tableDataType === 'yearProfit' ||
    props.chartConfig.option.tableDataType === 'perCapitaConsumption'
    // props.chartConfig.option.tableDataType === 'revenueExpenditure-meal'
    // props.chartConfig.option.tableDataType === 'revenueExpenditureForRoleId'
  ) {
    console.log("initTableData 11111", props.chartConfig.option.tableDataType);
    getSeriesData()
  } else if ((props.chartConfig.option.tableDataType === 'income' ||
    props.chartConfig.option.tableDataType === 'express'|| props.chartConfig.option.tableDataType === 'accountant') && option.selectValue) {
    getSeriesData()
    console.log("initTableData 2222", props.chartConfig.option.tableDataType);
  }
  else {
    if (props.chartConfig.option.tableDataType === 'revenueExpenditureForRoleId') {
      initData(props.chartConfig.option.dataList)
      option.value.yAxis.axisLabel.formatter = function (value, index) {
        // 在这里添加单位，例如：'元'
        return value + 'W';
      }
    } else if (props.chartConfig.option.tableDataType === 'revenueExpenditure-meal') {
      console.log('revenueExpenditure-meal666')
      const allData = revenueExpenditureMeal
      let dataKey
      if (option.selectValue) {
        dataKey = option.selectValue
      } else {
        // 如果没有指定 selectValue，使用第一个可用的键
        dataKey = Object.keys(allData)[0] || 'shangyouerzhong'
      }
      const result = allData[dataKey] || []
      initData(result)
    }
  }
}

// 渐变色处理
watch(
  () => chartEditStore.getEditCanvasConfig.chartThemeColor,
  (newColor: keyof typeof chartColorsSearch) => {
    try {
      option.value = props.chartConfig.option.chartOpts
    } catch (error) {
      console.log(error)
    }
  },
  {
    immediate: true
  }
)


watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initTableData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initTableData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseSelect'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initTableData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initTableData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

const { vChartRef } = useChartDataFetch(props.chartConfig, useChartEditStore)
</script>

<style scoped lang="scss">
.line-gradients-fund {
  .chart-wrap {
    background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
    padding: 15px 20px 10px;
  }
}
</style>