<template>
  <div class="">
    <collapse-item name="基础配置" :expanded="true">
      <setting-item-box name="标题" :alone="false" :leftStyle="leftStyle">
        <n-input v-model:value ="optionData.titleValue" type="text" placeholder="请输入标题" class="input-tag" show-count :maxlength="12"></n-input>
      </setting-item-box>
      <setting-item-box name="表类型">
        <n-select
          size="small"
          style="width: 200px"
          v-model:value="optionData.tableDataType"
          :options="selectTableOptions"
          @update:value="selectTableValueHandle"
        />
      </setting-item-box>     
    </collapse-item>
  </div> 
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue'
import { GlobalThemeJsonType } from '@/settings/chartThemes/index'
import { option } from './config'
import { CollapseItem, SettingItemBox } from '@/components/Pages/ChartItemSetting'

const props = defineProps({
  optionData: {
    type: Object as PropType<GlobalThemeJsonType>,
    required: true
  }
})

// 默认类型
const selectTableOptions = [
  {
    label: '资金监管总数据',
    value: "fundData",
    data: [{
        "title": "本月营收",
        "fee": "277762",
        "basis": "46.30%",
        "type": "up"
    },
    {
        "title": "本月支出",
        "fee": "258177.66",
        "basis": "46.46%",
        "type": "up"
    },
    {
        "title": "本月利润",
        "fee": "19584.34",
        "basis": "44.15%",
        "type": "up"
    }]
  },
  {
    label: '食堂监管总数据',
    value: "canteenData",
    data: [{
      title: "本月营收",
      fee: "98748",
      basis: "44.92%",
      type: "up",
    }, {
      title: "本月支出",
      fee: "91835.64",
      basis: "45.44%",
      type: "up",
    }, {
      title: "本月利润",
      fee: "6912.36",
      basis: "38.32%",
      type: "up",
    }]
  },
  {
    label: '财务公示总数据',
    value: "financeData",
    data: [{
      title: "总收入",
      fee: "217,942.00",
      basis: "5.5%",
      type: "up",
    }, {
      title: "总支出",
      fee: "124,623.34",
      basis: "-18.6%",
      type: "down",
    }, {
      title: "资金余额",
      fee: "620008",
      basis: "--",
      type: null,
    }]
  },
]

const selectTableValueHandle = (value: any) => {
  selectTableOptions.map(item => {
    if (item.value === value) {
      props.optionData.titleValue = item.label
      props.optionData.dataset = item.data
    }
  })
}

//自定义左侧style
const leftStyle:any = {
   width:"100px"
}
const seriesList = computed(() => {
  return props.optionData.series
})
</script>
<style scoped lang="scss">
 .input-tag{
  width: 200px;
 }
</style>