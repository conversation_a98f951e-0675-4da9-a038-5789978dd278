<template>
  <div class="">
    <collapse-item name="基础配置" :expanded="true">
      <setting-item-box name="标题" :alone="false">
        <n-input v-model:value ="optionData.titleValue" type="text" placeholder="请输入标题" class="input-tag" show-count :maxlength="12"></n-input>
      </setting-item-box>
      <setting-item-box name="数据类型" :alone="false">
        <n-select v-model:value="optionData.dataType" placeholder="请选择数据类型" class="input-tag" :options="optionData.typeList" :on-update:value="changeData" >
        </n-select>
      </setting-item-box>     
    </collapse-item>
  </div> 
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue'
import { GlobalThemeJsonType } from '@/settings/chartThemes/index'
import { option } from './config'
import { CollapseItem, SettingItemBox } from '@/components/Pages/ChartItemSetting'

const props = defineProps({
  optionData: {
    type: Object as PropType<GlobalThemeJsonType>,
    required: true
  }
})
console.log("CarouselImg", props.optionData)
// 数据改变
const changeData = (item: any, itemData:any) => {
  console.log("changeData", item, itemData);
  props.optionData.dataType = itemData.value
  props.optionData.titleValue = itemData.label
}

const seriesList = computed(() => {
  return props.optionData.series
})
</script>
<style scoped lang="scss">
 .input-tag{
  width: 200px;
 }
</style>