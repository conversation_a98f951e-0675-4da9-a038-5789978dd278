import { PublicConfigClass } from '@/packages/public'
import { MonitorVideo } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import { chartInitConfig } from '@/settings/designSetting'
import dataJson from './data.json'

export const option = {
  titleValue: "实时监控", // 标题
  type: 'video', // 类型
  videoList: [] as any[] , // 视频列表
  dataType: '', // 数据类型
  dataList: [] , // 数据列表,
  selectValue: '' as string | number,  // 选择项
  orgId : 0  as string | number
}

export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = MonitorVideo.key
  public chartConfig = cloneDeep(MonitorVideo)
  public attr = {...chartInitConfig, w:750, h:500, zIndex:-1}
  // 图表配置项
  public option = cloneDeep(option)
}
