# 可配置数据卡片组件使用说明

## 概述

`ConfigurableDataCards` 是一个高度可配置的数据展示组件，支持多种显示模式，可用于展示金额统计、数据统计等信息。组件采用配置驱动的方式，所有配置都在 `config.vue` 中定义。

## 主要特性

- ✅ **配置驱动**: 所有配置参数都在 `config.vue` 中定义，便于代码配置
- ✅ **多种预设**: 提供食堂物资金额合计、食材留样等预设配置
- ✅ **双显示模式**: 支持金额显示模式和统计显示模式
- ✅ **动态数据**: 支持接口获取动态数据
- ✅ **灵活样式**: 支持自定义颜色、图标等样式配置

## 配置结构

### config.ts (基础配置)
```typescript
export const option = {
  titleValue: '可配置数据卡片',
  cardDataType: 'canteen_materials', // 卡片类型
  isScroll: false, // 是否滚动，默认否
  dataset: [],
  cardConfig: {} // 卡片配置，由config.vue自动设置
}
```

### config.vue (详细配置)
所有具体的配置选项都在 `config.vue` 的 `selectCardOptions` 数组中定义：

```typescript
const selectCardOptions = [
  {
    label: '食堂物资金额合计',
    value: "canteen_materials",
    data: [...], // 静态数据
    width: 673,
    height: 200,
    displayMode: 'amount', // 显示模式
    dateTypeOptions: ['week', 'month'], // 时间选项
    iconColors: ['#0BF9FE', '#FFC73A', '#0BF9FE'], // 图标颜色
    amountColors: ['#0BF9FE', '#FFC73A', '#0BF9FE'], // 金额颜色
    showDivider: true, // 是否显示分隔线
    apiConfig: {
      apiFunction: 'apiGetMonthTotalDetail',
      apiParams: ['org_list', 'date_type']
    }
  }
]
```

## 预设配置

### 1. 食堂物资金额合计 (canteen_materials)
- **显示模式**: 金额模式
- **数据项**: 采购金额合计、入库成本合计、出库金额合计
- **特点**: 显示金额和环比数据，支持趋势图标
- **API**: `apiGetMonthTotalDetail`

### 2. 食材留样 (material_samples)
- **显示模式**: 统计模式
- **数据项**: 设备在线、当日留样品数、当日取样品数
- **特点**: 紧凑的横向布局，适合状态统计
- **API**: 无（静态数据）

### 3. 自定义配置 (custom)
- **显示模式**: 金额模式
- **数据项**: 可自定义
- **特点**: 基础配置，可作为新配置的起点

## 添加新配置

要添加新的配置类型，只需在 `config.vue` 的 `selectCardOptions` 数组中添加新项：

```typescript
{
  label: '新配置名称',
  value: "new_config_type",
  data: [
    {
      title: '项目1',
      value: '100',
      comparison: '+5%', // 仅金额模式需要
      icon: '' // 可选
    }
  ],
  width: 673,
  height: 200,
  displayMode: 'amount', // 'amount' | 'stats'
  dateTypeOptions: ['week', 'month'], // 时间筛选选项，空数组表示不显示
  iconColors: ['#0BF9FE'], // 图标颜色数组
  amountColors: ['#0BF9FE'], // 金额颜色数组（金额模式）
  statsValueColors: ['#0BF9FE'], // 统计值颜色数组（统计模式）
  showDivider: true, // 是否显示分隔线
  apiConfig: {
    apiFunction: 'apiFunction名称', // API函数名
    apiParams: ['参数1', '参数2'] // API参数列表
  }
}
```

## 接口集成

### API配置
在 `apiConfig` 中配置接口信息：
- `apiFunction`: API函数名称
- `apiParams`: API参数列表

### API映射
在 `index.vue` 中需要添加API映射：
```typescript
// API 映射
const apiMap: Record<string, any> = {
  apiGetMonthTotalDetail,
  // 添加新的API函数
  apiNewFunction
}
```

### 数据处理
组件会自动调用对应的API并处理返回数据，你可以在 `getTableData` 方法中添加数据处理逻辑。

## 样式自定义

### 颜色配置
- `iconColors`: 图标背景颜色数组
- `amountColors`: 金额文字颜色数组（金额模式）
- `statsValueColors`: 统计值文字颜色数组（统计模式）

### 尺寸配置
- `width`: 组件宽度
- `height`: 组件高度

### 显示选项
- `showDivider`: 是否显示分隔线
- `dateTypeOptions`: 时间筛选选项，空数组表示不显示时间筛选

## 使用示例

### 1. 在代码中配置
```typescript
// 创建组件实例
const config = new ConfigurableDataCardsConfig()

// 设置配置类型
config.option.cardDataType = 'canteen_materials'
config.option.titleValue = '自定义标题'

// 设置静态数据
config.option.dataset = [
  {
    title: '项目1',
    value: '1000',
    comparison: '+5%'
  }
]
```

### 2. 通过配置界面
1. 在组件配置面板中选择"选择类型"
2. 从下拉列表中选择预设配置
3. 组件会自动应用对应的配置和样式

## 注意事项

1. **向后兼容**: 新增配置参数时，要为旧配置提供默认值
2. **数据格式**: 确保数据项包含必要的字段（title, value等）
3. **API函数**: 新增API时要在 `apiMap` 中注册
4. **颜色数组**: 颜色数组长度应与数据项数量匹配
5. **显示模式**: 金额模式需要 `comparison` 字段，统计模式不需要

## 扩展建议

1. **新增显示模式**: 可以在模板中添加新的显示模式
2. **动画效果**: 可以添加数据变化的动画效果
3. **交互功能**: 可以添加点击事件和悬停效果
4. **响应式**: 可以根据屏幕尺寸调整布局
