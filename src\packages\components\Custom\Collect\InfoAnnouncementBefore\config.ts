import { PublicConfigClass } from '@/packages/public'
import { InfoAnnouncementBefore } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
export const includes = ['legend', 'xAxis', 'yAxis', 'grid']
import { chartInitConfig } from '@/settings/designSetting'
import dataJson from './data.json'

// 数据配置
export const option = {
  titleValue: '食安信息公示旧版',
  dataset: dataJson.certificateList,
  tableDataType: 'infoAnnouncement',
}
// 导出配置
export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = InfoAnnouncementBefore.key
  public chartConfig = cloneDeep(InfoAnnouncementBefore)
  public attr = { ...chartInitConfig, w: 596, h: 362, zIndex: -1 }
  // 图表配置项
  public option = cloneDeep(option)
}
