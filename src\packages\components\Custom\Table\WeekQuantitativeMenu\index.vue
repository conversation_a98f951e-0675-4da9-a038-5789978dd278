<template>
  <div :style="`width:${w}px;height:${h}px;`" class="week-quantitative-menu c-white">
    <!--标题-->
    <table-head :title="option.value.titleValue" :dateTypeOptions="currentTableConfig.dateTypeOptions"
      :date-type="option.dateType" :style="`width:${w}px`" @select-handle="selectHandle">
    </table-head>
    <!--内容-->
    <div class="table-wrap" :style="`height:${h - 75}px;`">
      <div v-if="currentTableConfig.totalConfig" class="table-total ps-flex col-center m-b-10">
        <img src="@/assets/images/chart/custom/total_consumption.png" alt="" srcset="">
        <div class="ps-flex col-center f-s-24 m-l-20">
          <div>{{ currentTableConfig.totalConfig.title }}：</div>
          <div class="total-price">￥{{ option.totalPrice }}</div>
        </div>
      </div>
      <!-- 所属组织筛选 -->
      <div class="org-filter">
        <div
          v-for="org in orgList.value"
          :key="org.id"
          class="btn-item"
          :style="getOrgButtonStyle(org.id)"
          @click="selectOrg(org.id)"
        >
          {{ org.name }}
        </div>
      </div>
      <div class="table-header">
        <div class="table-header-top"></div>
        <div class="table-header-bottom"></div>
        <div
          class="ps-flex row-between">
          <!-- 动态生成表头 -->
          <div
            v-for="column in currentTableConfig.tableSetting"
            :key="column.key"
            class="item-title"
            :class="getColumnClass(column)"
          >
            {{ column.label }}
          </div>
        </div>
      </div>

      <div class="scroll-wrap"
        :style="`height:${currentTableConfig.scrollHeight ? currentTableConfig.scrollHeight : 305}px;overflow: hidden;`">
        <vue3-seamless-scroll class="scroll" v-model='option.value.isScroll' :list="currentData.value" :step="0.5"
          :hover="true" :limit-scroll-num="currentTableConfig.limiScrollNum" :wheel="true">
          <div class='tag-item' v-for="(item, index) in currentData.value" :key="index">
            <div
              class="ps-flex flex-align-center p-l-5 p-r-5" style="
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0px 5px;
              ">
              <!-- 动态生成表格内容 -->
              <div
                v-for="column in currentTableConfig.tableSetting"
                :key="column.key"
                class="item-info"
                :class="getColumnClass(column)"
              >
                <div v-if="column.type === 'index'">{{ index + 1}}</div>
                <div v-if="column.type === 'text'" class="text">{{ item[column.key] }}</div>
                <div v-if="column.type === 'price'" class="text">￥{{ divide(item[column.key]) || '0.00'}}</div>
                <!-- 有什么特殊类型，自己再写吧 -->
              </div>
            </div>
          </div>
        </vue3-seamless-scroll>
      </div>

      <!-- 周按钮筛选 -->
      <div class="week-filter">
        <div
          v-for="(day, index) in weekList"
          :key="index"
          class="week-btn"
          :class="{ active: option.selectedWeekDay === day.value }"
          @click="selectWeekDay(day.value)"
        >
          {{ day.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive, ref } from 'vue'
import config, { includes, TableColumn, TableConfig } from './config'
// 导入背景图片
import activeOrgBtnBg from '@/assets/images/chart/custom/active_org_btn_bg.png'
import unActiveOrgBtnBg from '@/assets/images/chart/custom/un_active_org_btn_bg.png'
import cloneDeep from 'lodash/cloneDeep'
import { divide } from '@/utils'
import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'
import { TableHead } from '@/components/TableHead'
import { useRoute } from 'vue-router'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { weekList, mealTypes } from '@/utils/constants'
import { QuantitativeMenuWeek } from './data'
import {
  apiBackgroundFundSupervisionBigShieldGetOrgSubList,
  apiBackgroundFundSupervisionBigShieldRationRecipeWeekList
} from '@/api/path'

// API 映射
const apiMap: Record<string, any> = {
  apiBackgroundFundSupervisionBigShieldGetOrgSubList,
  apiBackgroundFundSupervisionBigShieldRationRecipeWeekList
}

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})

const route = useRoute()
const chartEditStore = useChartEditStore()

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);

// 配置
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option),
  totalPrice: 0, // 合计总金额
  dateType: 'month',
  channelId: Number(route.query.channel_id),
  orgId: Number(route.query.org_id),
  selectValue: 0,
  orgValue: '',
  selectedWeekDay: 1,
})

const orgList = reactive({
  value: Array<any>()
})

const totalData = reactive({
  value: Array<any>()
})

const currentData = reactive({
  value: Array<any>()
})

// 当前表格设置
const currentTableConfig = computed(() => {
  // 从 optionData 中获取 tableConfig，如果没有则返回默认配置
  console.log('option.value.tableConfig:', option.value.tableConfig)
  if (option.value.tableConfig) {
    return option.value.tableConfig as TableConfig
  }
  //  默认值
  return {
    label: '食堂带量食谱',
    value: "QuantitativeMenuWeek",
    data: [],
    width: 519,
    height: 830,
    scrollHeight: 600,
    limiScrollNum: 15,
    dateType: 'week',
    dateTypeOptions: ['week'],
    totalConfig: {
      title: '本周总消费'
    },
    apiConfig: {
      apiFunction: 'apiBackgroundFundSupervisionBigShieldRationRecipeWeekList',
      apiParams: ['org_id', 'channel_id', 'week_day']
    },
    tableSetting: [
      { label: '周次', key: 'week', type: 'text', flex: 1 },
      { label: '餐次', key: 'meal_type', type: 'text', flex: 1 },
      { label: '菜品名称', key: 'food_name', type: 'text', flex: 2 },
      { label: '重量', key: 'weight_total', type: 'text', flex: 1 },
      { label: '单价', key: 'food_price', type: 'price', flex: 1 },
      { label: '食材信息', key: 'ingredient_info', type: 'text', flex: 2 }
    ]
  }
})

// 获取列的CSS类名
const getColumnClass = (column: any) => {
  // 优先使用配置中的 flex 值
  if (column.flex) {
    return `flex-${column.flex}`
  }
  return 'flex-1'
}

// 获取数据
const getTableData = async (type?: any) => {
  let params: any = {}
  let apiConfig = currentTableConfig.value.apiConfig
  if (apiConfig.apiParams) {
    apiConfig.apiParams.forEach((paramKey: string) => {
      // 配置apiParams的时候，org_id、org_list、channel_id 三选一，指代的是左上角选择框，请注意。
      switch (paramKey) {
        case 'org_id':
          params.org_id = option.orgValue ? option.orgValue : option.selectValue ? option.selectValue : option.orgId
          break
        case 'org_list':
          params.org_list = option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined
          break
        case 'channel_id':
          params.channel_id = option.selectValue ? option.selectValue : option.channelId
          break
        case 'date_type':
          params.date_type = type ? type : option.dateType
          break
        default:
          params[paramKey] = null
      }
    })
  }
  // let apiFunction = apiMap[apiConfig.apiFunction]
  const res = await apiMap[apiConfig.apiFunction](params)
  if (res.code === 0) {
    if (res && res.data) {
      if (option.value.tableDataType === 'QuantitativeMenuWeek') {
        if (!res.data.results) return
        if (res.data.filter.org_id !== option.orgValue) return
        totalData.value = []
        for (let date in res.data.results) {
          // 获取日期的周数，按照周一、周二
          let week = weekList.find(day => day.value === new Date(date).getDay())?.label
          for (let item in res.data.results[date]) {
            let meal = mealTypes[item]
            res.data.results[date][item].map(food => {
              let weightTotal = 0
              let ingredient_info = food.ingredient_info_list.map((ingredient: any) => {
                weightTotal += ingredient.ingredient_weight
                return ingredient.ingredient_name
              })
              totalData.value.push({
                ...food,
                meal_type: meal,
                weekName: week,
                weekValue: new Date(date).getDay(),
                ingredient_info: ingredient_info.join('、'),
                weight_total: `${food.weight}g/${weightTotal}g`
              })
            })
          }
        }
        selectWeekDay(1)
      }
    }
  }
}

// 获取渠道组织的所属组织
const getOrgList = async () => {
  const res = await apiBackgroundFundSupervisionBigShieldGetOrgSubList({
    org_ids: option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined,
    channel_id: option.channelId
  })
  if (res && res.code === 0) {
    if (res && res.data) {
      orgList.value = res.data.results
      if (res.data.results && res.data.results.length > 0) {
        option.orgValue = res.data.results[0].id
        getTableData(option.dateType)
      }
    }
  }
}

// 获取筛选后的数据
const getFilteredData = () => {
  if (option.value.tableDataType === 'QuantitativeMenuWeek') {
    const allData = QuantitativeMenuWeek
    let dataKey
    if (option.selectValue) {
      dataKey = option.selectValue
    } else {
      // 如果没有指定 selectValue，使用第一个可用的键
      dataKey = Object.keys(allData)[0] || 'shangyouerzhong'
    }
    const result = allData[dataKey] || []
    console.log('getFilteredData 调试信息:', {
      tableDataType: option.value.tableDataType,
      selectValue: option.selectValue,
      dataKey,
      availableKeys: Object.keys(allData),
      resultLength: result.length,
      sampleResult: result.slice(0, 2)
    })
    return result
  }

  return []
}

const selectHandle = (type: string) => {
  let mapOrg = {
    shangyouerzhong: '上犹第二中学',
    shangyouyizhong: '上犹第一中学',
    anyuan: '安远实验中学',
    zhanggong: '章贡第一附属小学',
    dayu: '大余实验小学'
  }

  orgList.value = Object.keys(mapOrg).map(item => {
    return {
      id: item,
      name: mapOrg[item]
    }
  })
  option.orgValue = orgList.value[0].id

  option.dateType = type
  if (option.value.tableDataType === 'QuantitativeMenuWeek') {
    // 使用本地数据筛选
    const filteredData = getFilteredData()
    totalData.value = filteredData
    // 同时更新 currentData
    currentData.value = filteredData
    console.log('selectHandle 更新数据:', {
      type,
      totalDataLength: totalData.value.length,
      currentDataLength: currentData.value.length
    })
  } else {
    getTableData(type)
  }
}

// 选择周按钮
const selectWeekDay = (day: number) => {
  console.log(totalData.value, currentData.value)
  option.selectedWeekDay = day
  currentData.value = totalData.value.filter(item => item.weekValue === option.selectedWeekDay)
}

// 选择组织按钮
const selectOrg = (org: string) => {
  option.orgValue = org
  getTableData(option.dateType)
}

// 获取组织按钮样式
const getOrgButtonStyle = (orgId: string) => {
  const isActive = option.orgValue === orgId
  return {
    backgroundImage: `url(${isActive ? activeOrgBtnBg : unActiveOrgBtnBg})`,
    backgroundSize: '100% 100%',
    backgroundRepeat: 'no-repeat',
    color: isActive ? '#fff' : '#26BDFF',
    padding: '0 40px',
    height: '38px',
    lineHeight: '38px',
    textAlign: 'center' as const,
    fontSize: '18px',
    cursor: 'pointer',
    flexShrink: 0
  }
}

// 初始化
const initData = () => {
  // 确保 dateType 有默认值
  option.dateType = currentTableConfig.value.dateType || 'week'

  if (option.value.tableDataType === 'QuantitativeMenuWeek') {
    // 使用本地数据筛选
    const filteredData = getFilteredData()
    totalData.value = filteredData
    // 初始化时设置 currentData，显示所有数据或第一天的数据
    currentData.value = filteredData
    orgList.value = [{ id: 'shangyouerzhong', name: '上犹第二中学' }]
    option.orgValue = 'shangyouerzhong'
    console.log('初始化数据:', {
      totalDataLength: totalData.value.length,
      currentDataLength: currentData.value.length,
      sampleData: filteredData.slice(0, 3)
    })
  } else {
    if (option.selectValue) {
      getOrgList()
    } else {
      totalData.value = option.value.dataset || []
      currentData.value = option.value.dataset || []
    }
  }
}

// 调试数据流
const debugDataFlow = () => {
  console.log('=== WeekQuantitativeMenu 数据流调试 ===')
  console.log('1. 配置信息:', {
    tableDataType: option.value.tableDataType,
    dateType: option.dateType,
    selectValue: option.selectValue,
    orgValue: option.orgValue
  })
  console.log('2. 表格配置:', {
    tableSetting: currentTableConfig.value.tableSetting,
    tableSettingLength: currentTableConfig.value.tableSetting?.length
  })
  console.log('3. 数据状态:', {
    totalDataLength: totalData.value?.length,
    currentDataLength: currentData.value?.length,
    sampleTotalData: totalData.value?.slice(0, 2),
    sampleCurrentData: currentData.value?.slice(0, 2)
  })
  console.log('4. 组织列表:', {
    orgListLength: orgList.value?.length,
    orgList: orgList.value
  })
  console.log('=== 调试结束 ===')
}

// 页面加载
onMounted(() => {
  initData()
  // 延迟执行调试，确保数据已加载
  setTimeout(debugDataFlow, 500)
})

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseSelect'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

</script>

<style scoped lang="scss">
.week-quantitative-menu {

  .table-wrap {
    background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
    padding: 15px 20px 10px;
    overflow: hidden;
  }

  .table-total{
    height: 44px;
    img{
      height: 42px;
    }
    .total-price{
      font-size: 34px;
      color: #26BDFF;
      font-weight: 600;
      margin-top: 3px;
    }
  }

  .org-filter{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    overflow-x: auto;
    overflow-y: hidden;
    gap: 10px;
    padding: 2px 0 6px;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(127, 132, 145, 0.2);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #26BDFF;
      border-radius: 2px;

      &:hover {
        background: #26BDFF;
      }
    }

    // .btn-item 样式已移动到 JavaScript 中动态设置
    // 这样可以确保背景图片在打包后正确加载
  }

  .table-header {

    position: relative;
    height: 36px; /* 设置你的div高度 */
    overflow: hidden; /* 确保伪元素不会溢出div */
    font-size: 17px;
    margin-bottom: 5px;
    border: 1px solid #02A9FF66;
    background: #04253D;
    padding: 0 5px;
    box-sizing: border-box;
    .item-title {
      height: 36px;
      line-height: 36px;
      color: #26BDFF;
      text-align: center;
    }

    .table-header-top::before,
    .table-header-top::after,
    .table-header-bottom::before,
    .table-header-bottom::after {
      content: '';
      position: absolute;
      width: 6px;
      /* 短边框的宽度 */
      height: 6px;
      /* 短边框的高度 */
    }

    .table-header-top::before {
      top: 0;
      right: 0;
      border-top: 2px solid #06A5FF;
      border-right: 2px solid #06A5FF;
    }

    .table-header-top::after {
      top: 0;
      left: 0;
      border-top: 2px solid #06A5FF;
      border-left: 2px solid #06A5FF;
    }

    .table-header-bottom::before {
      bottom: 0;
      right: 0;
      border-bottom: 2px solid #06A5FF;
      border-right: 2px solid #06A5FF;
    }

    .table-header-bottom::after {
      bottom: 0;
      left: 0;
      border-bottom: 2px solid #06A5FF;
      border-left: 2px solid #06A5FF;
    }
  }

  .scroll-wrap {
    // overflow: hidden;
  }

  .scroll {
    /* 必需要设置合适的高,因为他的原理是往列表最后添加列表重复元素，所以这个组件高不要大于其内容原先的最大高度 */
    // height: 305px;
    // min-width: 450px;
    // overflow: hidden;
  }

  .tag-item {

    min-width: 479px;
    min-height: 40px;
    .item-info {
      height: 40px;
      line-height: 40px;
      color: #FFF;
      text-align: center;
      font-size: 16px;
      overflow: hidden;
      .text, .price{
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }

    .health-item-img {

      // height: 80px!important;
      // line-height: 80px!important;
      img {
        // margin-top: 12px;
      }
    }

    .item-img {
      width: 260px;
      height: 178px;
      padding: 5px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .tag-item:nth-child(2n-1) {
    background: #0E48724D;
  }

  .flex-6 {
    flex: 6;
  }
  .flex-5 {
    flex: 5;
  }
  .flex-4 {
    flex: 4;
  }

  .flex-3 {
    flex: 3;
  }

  .flex-2 {
    flex: 2;
  }

  .flex-1 {
    flex: 1;
  }

  .green-color {
    color: #0BF9FE !important;
  }

  .green2-color {
    color: #29DC7A !important;
  }

  .red-color {
    color: #FF4343 !important;
  }

  .blue-color {
    color: #26BDFF !important;
  }

  .yellow-color {
    color: #FF9A35 !important;
  }

  .f-s-24 {
    font-size: 24px;
  }

  .f-w-600 {
    font-weight: 600;
  }

  // 周按钮筛选样式
  .week-filter {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
    padding: 0 20px;

    .week-btn {
      height: 30px;
      line-height: 30px;
      padding: 0 3px;
      border: 1px solid #7F849182;
      transform: skew(-9deg);
      color: #fff;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 50px;
      text-align: center;

      &.active {
        color: #0BF9FE;
        border-color: #0BF9FE;
      }
    }
  }
}
</style>