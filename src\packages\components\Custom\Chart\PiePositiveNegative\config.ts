import { PublicConfigClass } from '@/packages/public'
import { PiePositiveNegative } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
export const includes = ['legend', 'xAxis', 'yAxis', 'grid']
import { chartInitConfig } from '@/settings/designSetting'

// 数据配置
export const option = {
  titleValue: '收入分布',
  dataList: [],
  tableDataType: 'DietDashboard',
  chartOpts: {
    title: {
      text: '合计营收 ￥0.00',
      left: 'center',
      top: '5',
      textStyle: {
        fontSize: 18,
        color: '#FFFFFF'
      }
    },
    grid: {
      top:'15%',
      left: '0%',
      right: '0%',
      bottom: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['储值消费', '补贴消费', '第三方消费', '财政补贴', '公益捐赠', '非营业性-其他', '营业性-其他'],
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(238, 238, 238, 0.3)' // 修改为半透明浅灰色
        }
      }
      // 刻度线间距
      // min: function (value) {
      //   return Math.min(value.min * 1.2, 0)
      // },
      // max: function (value) {
      //   return Math.max(value.max * 1.2, 0)
      // }
    },
    series: [
      {
        type: 'bar',
        // 这里给默认值无效，请去data.ts文件去修改
        data: [
          { value: 0, name: '储值消费', data_type: 'cz_price', itemStyle: { color:'#38B2FF' }, label: { position: 'top' }},
          { value: 0, name: '补贴消费', data_type: 'bt_price',itemStyle: { color:'#6456FF' }, label: { position: 'top' }},
          { value: 0, name: '第三方消费', data_type: 'ds_price', itemStyle: { color:'#0ADDE8' }, label: { position: 'top' } },
          { value: 0, name: '财政补贴', data_type: 'bz_price', itemStyle: { color:'#FFD364' }, label: { position: 'top' } },
          { value: 0, name: '公益捐赠', data_type: 'gy_price', itemStyle: { color:'#23588D' }, label: { position: 'top' } },
          { value: 0, name: '非营业性-其他', data_type: 'nqt_price', itemStyle: { color:'#5E87B5' }, label: { position: 'top' } },
          { value: 0, name: '营业性-其他', data_type: 'qt_price', itemStyle: { color:'#17E9AD' }, label: { position: 'top' } }
        ],
        // itemStyle: {
        //   borderRadius: [4, 4, 0, 0]
        // },
        barWidth: 40,
        label: {
          show: true,
          color: '#FFFFFF',
          distance: 10,// 与柱子的距离
          align: 'center',
          verticalAlign: 'middle'
        }
      }
    ]
  }
}
// 导出配置
export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = PiePositiveNegative.key
  public chartConfig = cloneDeep(PiePositiveNegative)
  public attr = { ...chartInitConfig, w: 596, h: 440, zIndex: -1 }
  // 图表配置项
  public option = cloneDeep(option)
}
