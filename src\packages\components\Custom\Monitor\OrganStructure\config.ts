import { PublicConfigClass } from '@/packages/public'
import { OrganStructure } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import { chartInitConfig } from '@/settings/designSetting'
import dataJson from './data.json'

export const option = {
  titleValue: '组织架构', // 标题
  dataType: 'schoolOrganStructure', // 数据类型
  typeList: cloneDeep(dataJson.typeList), // 数据类型列表
  dataList: [] as any[], // 数据列表
  selectValue: '' as string | number, // 选择值
  organId: 0 as string | number, // 组织id
  isTree: false // 是否树形结构
}

export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = OrganStructure.key
  public chartConfig = cloneDeep(OrganStructure)
  public attr = { ...chartInitConfig, w: 519, h: 290, zIndex: -1 }
  // 图表配置项
  public option = cloneDeep(option)
}
