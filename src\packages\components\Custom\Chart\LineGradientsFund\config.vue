<template>
  <div class="">
    <collapse-item name="基础配置" :expanded="true">
      <setting-item-box name="标题" :alone="false">
        <n-input v-model:value ="optionData.titleValue" type="text" placeholder="请输入标题" class="input-tag" show-count :maxlength="12"></n-input>
      </setting-item-box>
      <setting-item-box name="表类型">
        <n-select
          size="small"
          style="width: 200px"
          v-model:value="optionData.tableDataType"
          :options="selectTableOptions"
          @update:value="selectTableValueHandle"
        />
      </setting-item-box>
    </collapse-item>
  </div> 
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue'
import { GlobalThemeJsonType } from '@/settings/chartThemes/index'
import { CollapseItem, SettingItemBox } from '@/components/Pages/ChartItemSetting'
import { graphic } from 'echarts/core'
import dataJson from './data.json'

const props = defineProps({
  optionData: {
    type: Object as PropType<GlobalThemeJsonType>,
    required: true
  }
})

// 默认表类型
const selectTableOptions = [
  {
    label: '年度收支统计',
    value: "revenueExpenditure",
    data: dataJson.revenueExpenditureList1,
    legendData: ["收入", "支出"],
    colorList: ["rgba(11, 249, 254, 1)", "rgba(255, 199, 58, 1)"]
  },
  {
    label: '年度利润统计',
    value: "yearProfit",
    data: dataJson.yearProfitList1,
    legendData: ["今年", "去年"],
    colorList: ["rgba(15, 119, 255, 1)", "rgba(255, 199, 58, 1)"]
  },
  {
    label: '年度人均消费情况',
    value: "perCapitaConsumption",
    // data: dataJson.perCapitaConsumptionList1.fanyingyizhong,
    data: dataJson.perCapitaConsumptionList1,
    legendData: ["今年"],
    colorList: ["rgba(0, 133, 255, 1)"]
  },
  {
    label: '收支统计',
    value: "accountant",
    data: dataJson.accountantList1,
    legendData: ["收入", "支出"],
    colorList: ["rgba(11, 249, 254, 1)", "rgba(255, 199, 58, 1)"]
  },
  {
    label: '收入统计',
    value: "income",
    data: dataJson.accountantList2,
    legendData: ["收入"],
    colorList: ["rgba(11, 249, 254, 1)"]
  },
  {
    label: '支出统计',
    value: "express",
    data: dataJson.accountantList2,
    legendData: ["支出"],
    colorList: ["rgba(255, 199, 58, 1)"]
  },
  {
    label: '年度收支统计-膳食屏',
    value: "revenueExpenditure-meal",
    data: dataJson.revenueExpenditureList1,
    legendData: ["收入", "支出"],
    colorList: ["rgba(11, 249, 254, 1)", "rgba(255, 199, 58, 1)"]
  },
  {
    label: '年度收支统计',
    value: "revenueExpenditureForRoleId",
    data: dataJson.revenueExpenditureList1,
    legendData: ["收入", "支出"],
    colorList: ["rgba(11, 249, 254, 1)", "rgba(255, 199, 58, 1)"]
  }
]

// 应用颜色
const selectTableValueHandle = (value: any) => {
  selectTableOptions.map(item => {
    if (item.value === value) {
      props.optionData.titleValue = item.label
      props.optionData.dataList = item.data
      props.optionData.legendData = item.legendData
      props.optionData.colorList = item.colorList
    }
    // 要相同的标题 但是请求数据不一致需要处理
    if(item.value === "revenueExpenditure-meal") {
      props.optionData.titleValue = "年度收支统计"
    }
  })
  // if (value === 'revenueExpenditure') {
  //   props.optionData.chartOpts.series[1].lineStyle.color = "rgba(11, 249, 254, 1)"
  //   props.optionData.chartOpts.series[1].areaStyle.color = new graphic.LinearGradient(0, 0, 0, 1, [
  //     {
  //       offset: 0,
  //       color: "rgba(11, 249, 254, 1)"
  //     },
  //     {
  //       offset: 1,
  //       color: 'rgba(0,0,0,0)'
  //     }
  //   ])
  // }
  // if (value === 'yearProfit') {
  //   props.optionData.chartOpts.series[1].lineStyle.color = "rgba(15, 119, 255, 1)"
  //   props.optionData.chartOpts.series[1].areaStyle.color = new graphic.LinearGradient(0, 0, 0, 1, [
  //     {
  //       offset: 0,
  //       color: "rgba(15, 119, 255, 1)"
  //     },
  //     {
  //       offset: 1,
  //       color: 'rgba(0,0,0,0)'
  //     }
  //   ])
  // }
  // if (value === 'perCapitaConsumption') {
  //   props.optionData.chartOpts.series = [{
  //     type: 'line',
  //     smooth: false,
  //     label: {
  //       show: true,
  //       position: 'top',
  //       color: '#fff',
  //       fontSize: 12
  //     },
  //     lineStyle: {
  //       width: 3,
  //       type: 'solid',
  //       color: "rgba(11, 249, 254, 1)"
  //     },
  //     areaStyle: {
  //       opacity: 0.5,
  //       color: new graphic.LinearGradient(0, 0, 0, 1, [
  //         {
  //           offset: 0,
  //           color: "#0BF9FE"
  //         },
  //         {
  //           offset: 1,
  //           color: 'rgba(0,0,0,0)'
  //         }
  //       ])
  //     },
  //     itemStyle: {
  //       color: "#FFFFFF"
  //     }
  //   }]
  // }
}

</script>
<style scoped lang="scss">
 .input-tag{
  width: 200px;
 }
</style>