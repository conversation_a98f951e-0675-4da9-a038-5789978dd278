<template>
  <div :style="`width:${w}px;height:${h}px;`" class="sample-record c-white">
    <!--标题-->
    <bd-chart-head :title="option.value.titleValue" :style="`width:${w}px`"></bd-chart-head>
    <div>
    <!--内容-->
     <div class="">
      <div class="table-header">
        <div class="item-title item-title-meal">餐段</div>
        <div class="item-title">留样时间</div>
        <div class="item-title">菜品名称</div>
      </div>
      <vue3-seamless-scroll
        class="scroll"
        v-model='option.value.isScroll'
        :list="sampleRecordData.value"
        :step="0.5"
        :hover="true"
        :limit-scroll-num="3"
        :wheel="true"
        >
          <div class='tag-item db-box-border ps-flex flex-align-center' v-for="(item, index) in sampleRecordData.value" :key="index">
            <div class="item-info item-meal">{{item.meal_type_alias}}</div>
            <div class="item-info">{{item.reserved_date}}</div>
            <div class="item-info">{{item.food_name}}</div>
          </div>
      </vue3-seamless-scroll>
     </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { getSevenDateRange } from '@/utils'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import  { Vue3SeamlessScroll} from 'vue3-seamless-scroll'
import  BdChartHead from '@/components/BdChartHead/index.vue'
import { apiGetFoodReservedList } from '@/api/path'
import { StorageEnum } from '@/enums/storageEnum'
import { getLocalStorage } from '@/utils'
import { useRoute } from 'vue-router'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);
// 配置
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option)
})
console.log("option", option);

let organizationId
const info = getLocalStorage(StorageEnum.GO_SYSTEM_STORE)
const route = useRoute()
if (info) {
  organizationId = info.userInfo.use_org
} else {
  organizationId = Number(route.query.organization_id);
}


const sampleRecordData = reactive({
  value: Array<any>()
})

const dateRange = getSevenDateRange(7)

// 留样仪
const getFoodReservedList = async () => {
  const res = await apiGetFoodReservedList({
    org_ids: [organizationId],
    start_date: dateRange[0],
    end_date: dateRange[1],
  })
  if (res && res.data) {
    sampleRecordData.value = res.data.results.map(item => {
      let nameList:Array<String> = []
      item.food_detail.map(food => {
        nameList.push(food.name)
      })
      item.food_name = nameList.join("、")
      return item
    })
  }
}

// 页面加载
onMounted(() => {
  console.log("onMounted");  
  getFoodReservedList()
})

// dataset 无法变更条数的补丁
watch(
  () => props.chartConfig.option,
  (newData: any) => {
    console.log("newData有新得值更新", newData);
    option.value = cloneDeep(newData)
  },
  {
    immediate: true,
    deep: true
  }
)


</script>

<style scoped lang="scss">
.sample-record {
  color: #fff;
  overflow: hidden;
  .table-header{
    font-size: 20px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    .item-title{
      flex: 2;
      height: 36px;
      line-height: 25px;
      color: #05C788;
      text-align: center;
    }
    .item-title-meal{
      flex: 1;
    }
  }
  .tag-item {
    min-width: 479px;
    min-height: 50px;
    margin-top: 10px;
    .item-info{
      flex: 2;
      height: 36px;
      line-height: 36px;
      color: #FFF;
      text-align: center;
      font-size: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .item-meal{
      flex: 1;
    }
  }
  .scroll {
  /* 必需要设置合适的高,因为他的原理是往列表最后添加列表重复元素，所以这个组件高不要大于其内容原先的最大高度 */
    height: 305px;
    min-width: 450px;
    overflow: hidden;
  }

}
</style>