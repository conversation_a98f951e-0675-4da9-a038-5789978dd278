<template>
  <div :style="`width:${w}px;height:${h}px;`" class="pie-income c-white">
    <!--标题-->
    <fund-head :title="option.value.titleValue" :style="`width:${w}px`">
    </fund-head>
    <div class="pie-wrap">
      <!-- <div class="pie-chart" :style="`width:${h - 75}px;height:${h - 75}px;`" id="barConsumption">
        <v-chart ref="vChartRef" :init-options="initOptions" :theme="themeColor" :option="option.value.chartOpts"
          autoresize></v-chart>
      </div> -->
      <div class="pie-chart" :style="`width:${h - 75}px;height:${h - 75}px;`" id='barConsumption'>
      </div>
      <div class="pie-data">
        <div class="pie-data-item" v-for="(item, index) in labelList" :key="index">
          <div :class="['item-name', item.class]">{{ item.name }}</div>
          <div class="item-price">￥{{ formatAmount(item.value) }}, 占比{{ item.percentage }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, PropType, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import FundHead from '@/components/FundHead/index.vue'
import { apiBackgroundFundSupervisionBigShieldDietCostExpend } from '@/api/path'
import { useRoute } from 'vue-router'
import { formatAmount } from '@/utils'
import * as echarts from 'echarts'
import 'echarts-gl'
import { DIET_LABEL_LIST, costExpenditure } from './data'
const route = useRoute()

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)

use([CanvasRenderer])
const chartEditStore = useChartEditStore()

const option = reactive({
  value: props.chartConfig.option,
  dateType: 'month',
  channelId: Number(route.query.channel_id),
  orgId: Number(route.query.org_id),
  selectValue: 0
})

let configData = reactive({
  totalValue: '￥800',
  middleText: '合计支出'
})

// 数据列表
const labelList = ref<Array<any>>([])

// 计算合计支出
const calculateTotalExpense = () => {
  const total = labelList.value.reduce((sum, item) => sum + (item.value || 0), 0)
  configData.totalValue = '￥' + formatAmount(total)
  console.log('更新合计支出:', configData.totalValue, '总金额:', total)
  return total
}

// 获取数据
const getTableData = async (type?: any) => {
  if (!option.selectValue) {
    return
  }
  let api: any;
  let params: any = {};
  switch (option.value.tableDataType) {
    // 膳食大屏成本支出占比-本月-3D图
    case 'costExpenditure':
      params = {
        org_list: option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined,
        org_id: option.selectValue ? option.selectValue : option.orgId ? option.orgId : undefined,
        channel_id: option.channelId,
      }
      api = apiBackgroundFundSupervisionBigShieldDietCostExpend
    break
  }
  const res = await api(params)
  if (res && res.code === 0) { 
    let resultList = res.data ? res.data : []
    if (resultList.length > 0) {
      labelList.value = labelList.value.map((item: any) => {
        const findItem = resultList.find((i: any) => i.key === item.key)
        return {
          ...item,
          value: Number(findItem.consume_price),
          percentage: findItem?.percentage || 0
        };
      });
      // 计算合计支出
      calculateTotalExpense()
      nextTick(initChart)
    } else {
      // 处理空数据情况
      labelList.value.forEach(item => {
        item.value = 0
        item.percentage = 0
      })
      configData.totalValue = '￥' + 0.00
      nextTick(initChart)
    }
  } else {
    // 错误处理
    labelList.value.forEach(item => {
      item.value = 0
      item.percentage = 0
    });
    configData.totalValue = '￥' + 0.00
    nextTick(initChart)
  }
}
// 生成配置options
function getPie3D (pieData: any, internalDiameterRatio: any, configData: any) {
  let series:any[] = []
  let sumValue = 0
  let startValue = 0
  let endValue = 0
  let legendData:any[] = []
  let k = 1 - internalDiameterRatio;
  // 为每一个饼图数据，生成一个 series-surface(参数曲面) 配置
  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value;
    let seriesItem:any = {
      //系统名称
      name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
      type: 'surface',
      //是否为参数曲面（是）
      parametric: true,
      //曲面图网格线（否）上面一根一根的
      wireframe: {
        show: false
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: k
      },
      //设置饼图在容器中的位置(目前没发现啥用)
      // center: ['80%', '100%'],
      // radius: '60%',
    };

    //曲面的颜色、不透明度等样式。
    if (typeof pieData[i].itemStyle != 'undefined') {
      let itemStyle:any = {}
      typeof pieData[i].itemStyle.color != 'undefined' ? itemStyle.color = pieData[i].itemStyle.color : null
      typeof pieData[i].itemStyle.opacity != 'undefined' ? itemStyle.opacity = pieData[i].itemStyle.opacity : null
      seriesItem.itemStyle = itemStyle
    }
    series.push(seriesItem);
  }

  // 计算每个扇区的相对高度
  const values = series.map(item => item.pieData.value);
  const maxValue = Math.max(...values);
  const minValue = Math.min(...values);
  const valueRange = maxValue - minValue;

  // 设置高度范围
  const minHeight = 15;  // 最小高度系数
  const maxHeight = 50;  // 最大高度系数
  const heightRange = maxHeight - minHeight;

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  legendData = [];
  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value
    series[i].pieData.startRatio = startValue / sumValue
    series[i].pieData.endRatio = endValue / sumValue

    // 计算当前扇区的相对高度
    let relativeHeight: number;
    if (valueRange === 0) {
      // 所有值相同时，使用中等高度
      relativeHeight = (minHeight + maxHeight) / 2;
    } else {
      // 使用对数缩放来增强视觉差异
      const currentValue = series[i].pieData.value;
      const logCurrent = Math.log10(currentValue + 1);
      const logMax = Math.log10(maxValue + 1);
      const logMin = Math.log10(minValue + 1);
      const logRange = logMax - logMin;

      if (logRange < 0.1) {
        // 对数范围太小，使用线性缩放
        const normalizedValue = (currentValue - minValue) / valueRange;
        relativeHeight = minHeight + (heightRange * normalizedValue);
      } else {
        // 使用对数缩放
        const normalizedLog = (logCurrent - logMin) / logRange;
        relativeHeight = minHeight + (heightRange * normalizedLog);
      }
    }

    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      relativeHeight
    );

    console.log(`扇区 ${i} (${series[i].pieData.name}):`, {
      value: series[i].pieData.value,
      relativeHeight: Math.round(relativeHeight * 100) / 100
    });

    startValue = endValue;
  }

  //(第二个参数可以设置你这个环形的高低程度)
  let boxHeight = getHeight3D(series, 10);//通过传参设定3d饼/环的高度
  // 准备待返回的配置项，把准备好的 legendData、series 传入。
  let option = {
    //图例组件
    legend: {
      data: legendData,
      //图例列表的布局朝向。
      orient: 'horizontal',
      center: 0,
      bottom: 0,
      //图例文字每项之间的间隔
      itemGap: 15,
      textStyle: {
        color: '#A1E2FF',
        fontSize: '12px'
      },
      itemHeight: 10, // 修改icon图形大小
      itemWidth: 10, // 修改icon图形大小
      show: true,
      icon: 'circle'
    },

    labelLine: {
      show: true,
      lineStyle: {
        color: '#7BC0CB'
      }
    },
    tooltip: {
      show: false,
      // formatter: params => {
      //   if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
      //     let pieData = option.series[params.seriesIndex].pieData;
      //     return `${pieData.name}<br/>数值: ${pieData.value}<br/>占比: ${pieData.percentage}`;
      //   }
      //   return '';
      // }
    },
    //这个可以变形
    xAxis3D: {
      min: -1,
      max: 1
    },
    yAxis3D: {
      min: -1,
      max: 1
    },
    zAxis3D: {
      min: -1,
      max: 1
    },
    //此处是修改样式的重点
    grid3D: {
      show: false,
      boxHeight: boxHeight, //圆环的高度
      //这是饼图的位置
      top: '10.5%',
      left: '0%',
      viewControl: { //3d效果可以放大、旋转等，请自己去查看官方配置
        alpha: 25, //角度(这个很重要 调节角度的)
        distance: 160,//调整视角到主体的距离，类似调整zoom(这是整体大小)
        rotateSensitivity: 0, //设置为0无法旋转
        zoomSensitivity: 0, //设置为0无法缩放
        panSensitivity: 0, //设置为0无法平移
        autoRotate: true //自动旋转
      }
    },
    graphic: [
      {
        type: 'text',
        left: 'center',
        top: '45%', // 第一行
        style: {
          text: configData.totalValue, // 第一行内容
          fill: '#fff',
          font: 'bold 22px "Microsoft YaHei", sans-serif',
          textAlign: 'center',
          textVerticalAlign: 'middle' // 垂直居中
        }
      },
      {
        type: 'text',
        left: 'center',
        top: '53%', // 第二行，略微下移
        style: {
          text: configData.middleText, // 第二行内容
          fill: '#FFFFFF',
          font: '16px "Microsoft YaHei", sans-serif',
          textAlign: 'center',
          textVerticalAlign: 'middle' // 垂直居中
        }
      }
    ],
    series: series
  };
  return option;
}
// 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
function getParametricEquation (startRatio: number, endRatio: number, isSelected: boolean, isHovered: boolean, k: number, h: number) {
  // 计算
  let midRatio = (startRatio + endRatio) / 2;
  let startRadian = startRatio * Math.PI * 2;
  let endRadian = endRatio * Math.PI * 2;
  let midRadian = midRatio * Math.PI * 2;
  // 如果只有一个扇形，则不实现选中效果。
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false;
  }
  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = typeof k !== 'undefined' ? k : 1 / 3;
  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
  let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  let hoverRate = isHovered ? 1.05 : 1;
  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32
    },
    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20
    },
    x: function (u, v) {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    y: function (u, v) {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    z: function (u, v) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * .1;
      }
      return Math.sin(v) > 0 ? 1 * h * .1 : -1;
    }
  };
}

//获取3d饼图的最高扇区的高度
function getHeight3D(series: any[], baseHeight: number) {
  if (!series.length) return baseHeight;

  // 获取所有数值
  const values = series.map(item => item.pieData.value);
  const maxValue = Math.max(...values);
  const minValue = Math.min(...values);

  // 设置高度范围
  const minHeight = 8;  // 最小高度
  const maxHeight = 25; // 最大高度

  // 如果所有值都相同，返回中等高度
  if (maxValue === minValue) {
    return (minHeight + maxHeight) / 2;
  }

  // 计算高度比例，确保有明显的视觉差异
  const valueRange = maxValue - minValue;
  const heightRange = maxHeight - minHeight;

  // 使用对数缩放来处理大数值，增强视觉差异
  const logMax = Math.log10(maxValue + 1);
  const logMin = Math.log10(minValue + 1);
  const logRange = logMax - logMin;

  // 如果对数范围太小，使用线性缩放
  if (logRange < 0.1) {
    return minHeight + (heightRange * 0.8); // 返回较高的固定值
  }

  // 返回基于最大值的高度，使用对数缩放
  const normalizedHeight = (logMax - logMin) / Math.max(logRange, 0.1);
  const finalHeight = minHeight + (heightRange * normalizedHeight);

  console.log('3D高度计算:', {
    maxValue,
    minValue,
    valueRange,
    logMax,
    logMin,
    logRange,
    finalHeight: Math.round(finalHeight * 100) / 100
  });

  return Math.min(maxHeight, Math.max(minHeight, finalHeight));
}

// 获取筛选后的数据
const getFilteredData = () => {
  if (option.value.tableDataType === 'costExpenditure') {
    const allData = costExpenditure
    let dataKey: string
    if (option.selectValue) {
      dataKey = String(option.selectValue)
    } else {
      // 如果没有指定 selectValue，使用第一个可用的键
      dataKey = 'shangyouerzhong'
    }
    labelList.value = allData[dataKey]

    // 计算合计支出
    calculateTotalExpense()

    console.log("labelList.value", labelList.value, dataKey, allData[dataKey]);
    nextTick(initChart)
  }

  return []
}

const initData = () => {
  console.log('labelList.value', labelList.value, option.value.tableDataType === 'costExpenditure', option.value.tableDataType)
  if (option.value.tableDataType === 'costExpenditure') {
    // 使用本地数据筛选
    getFilteredData()
  } else {
    getTableData()
  }
}
// 页面加载
onMounted(() => {
  initData()
})

// 将初始化逻辑提取为单独函数
const initChart = () => {
  const chartDom = document.getElementById('barConsumption')
  if (!chartDom) return
  
  // 清除旧实例
  const oldChart = echarts.getInstanceByDom(chartDom)
  if (oldChart) oldChart.dispose()

  // 初始化新实例
  const myChart = echarts.init(chartDom)
  const optionInit = getPie3D(labelList.value, 0.88, configData)
  // 根据容器尺寸自动调整
  // optionInit.grid3D.viewControl.distance = Math.min(containerHeight, containerWidth * 0.8);
  // optionInit.grid3D.boxHeight = getHeight3D(optionInit.series, 10);
  myChart.setOption(optionInit) 
}

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        getTableData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        getTableData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseSelect'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

// watch监听宽高
watch(
  () => [props.chartConfig.attr.w, props.chartConfig.attr.h],
  () => nextTick(initChart),
  { deep: true }
)
// watch监听tableDataType
watch(
  () => option.value.tableDataType,
  (newData: string) => {
    if (newData) {
      labelList.value = getLabelListBytableDataType(newData)
      nextTick(initChart)
    }
  },
  {
    immediate: true,
    deep: true
  }
)
// 根据类型生成对应的数据列表
function getLabelListBytableDataType(tableDataType:string) {
  switch (tableDataType) {
    case 'costExpenditure':
      return cloneDeep(DIET_LABEL_LIST)
    default:
      return []
  }
}
// const { vChartRef } = useChartDataFetch(props.chartConfig, useChartEditStore)
</script>

<style scoped lang="scss">
.pie-income {
  .pie-wrap {
    position: relative;
    background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
    display: flex;
    padding: 0 15px;

    .pie-chart {
      flex: 2;
      background: url('@/assets/images/chart/custom/pie_costExpend_buttom_bg.png') no-repeat;
      background-position: center calc(100% - 20px);
    }

    .pie-data {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 20px;

      .pie-data-item {
        margin-bottom: 10px;

        .item-price {
          font-size: 16px;
          font-weight: bold;
        }

        .item-name {
          margin-left: 19px;
          position: relative;
          font-size: 14px;
        }

        .item-name::before {
          content: '';
          width: 14px;
          height: 14px;
          position: absolute;
          top: 4px;
          left: -18px;
          border-radius: 4px;
        }

        .green {
          color: #FD4B82;
        }

        .green::before {
          background: #FD4B82;
        }

        .grey {
          color: #5E8FED;
        }

        .grey::before {
          background: #5E8FED;
        }

        .purple{
          color: #6456FF;
        }

        .purple::before{
          background-color: #6456FF;
        }

        .skyBlue{
          color: #38B2FF
        }

        .skyBlue::before  {
          background-color: #38B2FF
        }

        .pink{
          color: #D2A3FF;
        }

        .pink::before  {
          background-color: #D2A3FF
        }
      }
    }
  }
}
</style>