<template>
  <div :style="`width:${w}px;height:${h}px;`" class="config-info-data-cards c-white">
    <!--标题-->
    <table-head
      v-if="currentCardConfig.showTableHead"
      :title="option.value.titleValue"
      :dateTypeOptions="currentDateTypeOptions"
      :date-type="option.dateType"
      :style="`width:${w}px`"
      @select-handle="selectHandle">
    </table-head>
    
    <div class="content-data">
      <!-- 晨检情况 -->
      <div v-if="currentDisplayMode === 'morning_check'" class="morning-check-card">
        <div class="total-wrap">
          <div class="total-title">当日晨检总数</div>
          <img src="@/assets/images/chart/custom/morning_check.png" alt="" srcset="">
          <div class="total-count">{{ infoData.value.total || 0 }}</div>
        </div>
        <div class="m-r-10">
          <div class="ps-flex m-b-10">
            <img src="@/assets/images/chart/custom/normal_icon.png" alt="" srcset="">
            <div class="m-l-5">
              <div class="title">正常人数：</div>
              <div class="normal-count">{{ infoData.value.normal || 0 }}</div>
            </div>
          </div>
          <div class="ps-flex">
            <img src="@/assets/images/chart/custom/abnormal_icon.png" alt="" srcset="">
            <div class="m-l-5">
              <div class="title">异常人数：</div>
              <div class="abnormal-count">{{ infoData.value.abnormal || 0 }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { isPreview } from '@/utils'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import { TableHead } from '@/components/TableHead'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { useRoute } from 'vue-router'
import {
  apiBackgroundFundSupervisionBigShieldMorningCheckData
} from '@/api/path'

// API 映射
const apiMap: Record<string, any> = {
  apiBackgroundFundSupervisionBigShieldMorningCheckData
}


const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})

const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)
const route = useRoute()
const chartEditStore = useChartEditStore()

// 长宽获取
const { w, h } = toRefs(props.chartConfig.attr)

// 配置
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option),
  channelId: Number(route.query.channel_id),
  orgId: Number(route.query.org_id),
  selectValue: 0,
  dateType: ''
})

const infoData = reactive<any>({
  value: Object
})

// 获取默认卡片配置
const getDefaultCardConfig = () => {
  return {
    label: '可配置信息卡片',
    value: "custom",
    data: {
      total: 0,
      normal: 0,
      abnormal: 0
    },
    width: 288,
    height: 178,
    displayMode: '',
    showTableHead: false,
    dateType: '',
    dateTypeOptions: ['week'],
    apiConfig: {
      apiFunction: '',
      apiParams: ['']
    }
  }
}

// 当前卡片配置
const currentCardConfig = computed(() => {
  const config = option.value.cardConfig || getDefaultCardConfig()
  // 确保所有必要属性都有默认值
  return {
    ...getDefaultCardConfig(),
    ...config
  }
})

// 当前API 配置
const apiConfig = computed(() => {
  const config = currentCardConfig.value as any
  return config.apiConfig || {
    apiFunction: '',
    apiParams: []
  }
})

// 显示数据
const displayData = computed(() => {
  const data = (option.value.dataset as any) || {}
  // 确保所有必要的属性都有默认值
  return {
    total: data.total || 0,
    normal: data.normal || 0,
    abnormal: data.abnormal || 0,
    ...data
  }
})

// 当前显示模式
const currentDisplayMode = computed(() => {
  const config = currentCardConfig.value as any
  return config.displayMode || 'amount'
})

// 当前时间选项
const currentDateTypeOptions = computed(() => {
  const config = currentCardConfig.value as any
  return config.dateTypeOptions || ['week']
})

// 获取数据
const getTableData = async (type?: any) => {
  let params: any = {}
  if (apiConfig.value.apiParams) {
    apiConfig.value.apiParams.forEach((paramKey: string) => {
      // 配置apiParams的时候，org_id、org_list、channel_id 三选一，指代的是左上角选择框，请注意。
      switch (paramKey) {
        case 'org_id':
          params.org_id = option.selectValue ? option.selectValue : option.orgId
          break
        case 'org_list':
          params.org_list = option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined
          break
        case 'channel_id':
          params.channel_id = option.selectValue ? option.selectValue : option.channelId
          break
        case 'date_type':
          params.date_type = type ? type : option.dateType
          break
        default:
          params[paramKey] = null
      }
    })
  }
  const res = await apiMap[apiConfig.value.apiFunction](params)
  if (res.code === 0) {
    if (res && res.data) {
      if (option.value.cardDataType === 'morning_check') {
        infoData.value = {
          total: res.data.total_count,
          normal: res.data.success_count,
          abnormal: res.data.failed_count
        }
      }
    }
  }
}

// 事件处理
const selectHandle = (type: string) => {
  option.dateType = type
  // 这里可以添加数据获取逻辑
  getTableData(type)
}

// 初始化
const initData = () => {
  option.dateType = currentCardConfig.value.dateType
  if (option.selectValue) {
    getTableData(option.dateType)
  } else {
    infoData.value = displayData.value
  }

}

// 页面加载
onMounted(() => {
  initData()
})

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)
</script>

<style scoped lang="scss">
.config-info-data-cards {
  background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
  border: 1px solid;
  border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7)) 1;
  box-sizing: border-box;

  .content-data {
    padding: 5px;
    gap: 20px;
  }

  .morning-check-card{
    display: flex;
    justify-content: space-between;
    text-align: center;
    width: 100%;
    margin-top: 10px;

    .total-wrap{
      position: relative;
      padding-top: 10px;
    }

    .total-count {
      font-size: 34px;
      position: absolute;
      transform: translate(-50%, -50%);
      top: 40%;
      left: 50%;
    }

    .total-title {
      font-size: 12px;
      width: 100%;
      position: absolute;
      transform: translate(-50%, -50%);
      top: 5%;
      left: 50%;
    }
    
    .title {
      font-size: 12px;
    }

    .normal-count {
      font-size: 16px;
      color: #0BF9FE;
      text-align: left;
      font-weight: bold;
    }
    .abnormal-count {
      font-size: 16px;
      color: #FF4343;
      text-align: left;
      font-weight: bold;
    }
  }
}
</style>
