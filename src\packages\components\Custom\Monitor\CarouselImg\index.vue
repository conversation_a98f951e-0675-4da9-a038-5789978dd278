<template>
  <div :style="`width:${w}px;height:${h}px;`" class="carouse-img m-t-10">
    <fund-head :title="option.value.titleValue" :style="`width:${w}px`" @selectHandle="handlerClickMonitor">
    </fund-head>
    
    <div class="carouse-list" :style="`width:${w}px;height:${contentHeight}px`" v-loading="dataLoading">
      <n-carousel 
        :effect="(imgList && imgList.length > 2 && dataSetType !== 'employeeMonitoring') ? 'card':'slide'" 
        :centered-slides="!imgList || imgList.length === 1"
        :style="`height: ${contentHeight}px;`"   
        :slides-per-view=" (imgList && imgList.length > 2 && dataSetType !== 'employeeMonitoring') ? 'auto' : '2' " 
        :prev-slide-style=" (imgList && imgList.length > 2 && dataSetType !== 'employeeMonitoring') ? 'transform: translateX(-97%) translateZ(-374px)' : '' "
        :next-slide-style=" (imgList && imgList.length > 2 && dataSetType !== 'employeeMonitoring') ? 'transform: translateX(-3%) translateZ(-374px)' : '' " 
        :show-dots="true" 
        dot-type="line" 
        dot-placement="bottom"
        :space-between="-15"
        :autoplay="true"
        :loop="true"
        draggable>
        <n-carousel-item  v-for="(item, index) in imgList" :key="index" class="carousel-item">
          <div class="ps-flex flex-column row-center">
            <div v-if="item.name" class="carousel-txt">{{ item.name }}</div>
            <div :style="{ width: (imgList && imgList.length > 2 && dataSetType !== 'employeeMonitoring') ? '186px' : '215px', height: (imgList && imgList.length > 2 && dataSetType !== 'employeeMonitoring') ? '128px' : '146px', margin: item.name ? '35px auto':'20px 20px 20px 10px' }">
              <img v-if="item.image" class="carousel-img" :src="item.image">
            </div> 
          </div>
        </n-carousel-item>
        <!-- <template #dots="{ total, currentIndex, to }">
          <ul class="custom-dots">
            <li
              v-for="index of total"
              :key="index"
              :class="{ ['is-active']: currentIndex === index - 1 }"
              @click="to(index - 1)"
            />
          </ul>
        </template> -->
      </n-carousel>
      
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, watchEffect, onMounted, reactive } from 'vue'
import config from './config'
import { useChartDataFetch } from '@/hooks'
import { CreateComponentType } from '@/packages/index.d'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import cloneDeep from 'lodash/cloneDeep'
import { controlGlobalConfigType } from '@/store/modules/chartEditStore/chartEditStore.d'
import { CarouselImg } from './index'
import { useControlUpdate } from '@/hooks/useControlUpdate.hook'
import { FundHead } from '@/components/FundHead'
import dData from './data.json'
import {
  apiBackgroundFundSupervisionBigShieldSchoolQualificationListPost, apiBackgroundFundSupervisionBigShieldHealthImageListPost,
  apiBackgroundFundSupervisionBigShieldSupplierQualificationListPost, apiBackgroundFundSupervisionBigShieldFoodTraceabilityListPost,
  apiBackgroundFundSupervisionBigShieldEnterpriseQualificationListPost

} from '@/api/path'
import { useRoute } from 'vue-router'
import { parseTime } from '@/utils/time'

import zizhi1 from "@/assets/images/newDemo/zizhi/zizhi1.jpg"
import zizhi2 from "@/assets/images/newDemo/zizhi/zizhi2.jpg"
import zizhi3 from "@/assets/images/newDemo/zizhi/zizhi3.jpg"
import zizhi4 from "@/assets/images/newDemo/zizhi/zizhi4.jpg"
import zizhi5 from "@/assets/images/newDemo/zizhi/zizhi5.jpg"
import fuwu1 from "@/assets/images/newDemo/zizhi/fuwu1.jpg"
import fuwu2 from "@/assets/images/newDemo/zizhi/fuwu2.jpg"
import fuwu3 from "@/assets/images/newDemo/zizhi/fuwu3.jpg"
import congye1 from "@/assets/images/newDemo/zizhi/congye1.jpg"
import congye2 from "@/assets/images/newDemo/zizhi/congye2.jpg"
import congye3 from "@/assets/images/newDemo/zizhi/congye3.png"
import congye4 from "@/assets/images/newDemo/zizhi/congye4.png"
import congye5 from "@/assets/images/newDemo/zizhi/congye5.png"
import peisong1 from "@/assets/images/newDemo/zizhi/peisong1.jpg"
import peisong2 from "@/assets/images/newDemo/zizhi/peisong2.jpg"
import peisong3 from "@/assets/images/newDemo/zizhi/peisong3.jpg"
import suyuan1 from "@/assets/images/newDemo/zizhi/suyuan1.jpg"
import suyuan2 from "@/assets/images/newDemo/zizhi/suyuan2.png"
import suyuan3 from "@/assets/images/newDemo/zizhi/suyuan3.jpg"
import suyuan4 from "@/assets/images/newDemo/zizhi/suyuan4.png"

const route = useRoute()

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
// 长宽
const { w, h } = toRefs(props.chartConfig.attr)
const contentHeight = ref((h.value - 42))
console.log("w", w, "h", h);
// 控件的key
const componentKey = CarouselImg.key
// 图片列表
const imgList = ref<Array<any>>()
// 设置的数据类型
const dataSetType = ref()
// 数据加载中
const dataLoading = ref(false)

// 配置
const option = reactive({
  value: cloneDeep(props.chartConfig.option)
})
console.log("option", option);


// 点击
const handlerClickMonitor = (item: any) => {
  console.log("handlerClickMonitor", item);
}

//  保存的数据
const chartEditStore = useChartEditStore()
const selectValue = chartEditStore.getControlGlobalConfigByKey('ChooseTree')
// 根据类型获取数据
const getDataByType = async () => {

  let api;
  let params = {
    channel_id: option.value.selectValue ? option.value.selectValue : Number(route.query.channel_id),
    role_id: Number(route.query.role_id)
  }
  let startDate = new Date()
  switch (dataSetType.value) {
    // schoolQualification
    case 'schoolQualification':
      api = apiBackgroundFundSupervisionBigShieldSchoolQualificationListPost(params)
      break;
    // 从业人员监控
    case 'employeeMonitoring':
      api = await apiBackgroundFundSupervisionBigShieldHealthImageListPost(params)
      break;
    //  服务企业资源
    case 'serviceEnterpriseResources':
      api = await apiBackgroundFundSupervisionBigShieldEnterpriseQualificationListPost(params)
      break;
    //  食材配送企业资质
    case 'foodDeliveryEnterpriseQualification':
      api = await apiBackgroundFundSupervisionBigShieldSupplierQualificationListPost(params)
      break;
    // 食材溯源
    case 'foodTraceability':
      startDate.setDate(startDate.getDate() - 7)
      Reflect.set(params, 'start_date', parseTime(startDate, '{y}-{m}-{d}'))
      Reflect.set(params, 'end_date',  parseTime(new Date(), '{y}-{m}-{d}'))
      api = await apiBackgroundFundSupervisionBigShieldFoodTraceabilityListPost(params)
      break;
  }

  dataLoading.value = true
  let res = await api
  dataLoading.value = false

  if (res && res.code === 0) {
    let data = res.data || []
    if(dataSetType.value === 'schoolQualification') {
      data = data.map((item: any) => {
        return {
          image: item.image,
          name: item.qualification_type_alias
        }
      })
    }else
    if (dataSetType.value === 'foodTraceability') {
      data = data.map((item: any) => {
        return {
          image: item
        }
      })
    }else if (dataSetType.value === 'employeeMonitoring') {
      data = data.map((item: any) => {
        return {
          image: item.health_image
        }
      })
    } else if (dataSetType.value === 'serviceEnterpriseResources'){
      let newList:Array<any> = []
      data.forEach((item: any) => {
        const name:string = item.name 
        const businessLicense:Array<any> = item.business_license || []
        if (businessLicense.length) { 
          businessLicense.forEach((item: any) => {
            newList.push({
              name,
              image: item
            })
          })
        }
      })
      imgList.value = cloneDeep(newList)
      return
    }
    imgList.value = cloneDeep(data)
  } else {
    imgList.value = []
  }
}
const initData = async () => {
  // 获取数据
  let type = props.chartConfig.option.dataType
  dataSetType.value = type
  if (dataSetType.value === 'schoolQualification') {
    imgList.value = [{
      image: zizhi1,
      name: '食品经营许可证'
    }, {
      image: zizhi2,
      name: '食品经营许可证'
    }, {
      image: zizhi3,
      name: '食品经营许可证'
    }, {
      image: zizhi4,
      name: '食品经营许可证'
    }, {
      image: zizhi5,
      name: '食品经营许可证'
    }]
    console.log('imgList', imgList)
  } else if (dataSetType.value === 'employeeMonitoring') {
    imgList.value = [{
      image: congye1
    }, {
      image: congye2
    }, {
      image: congye3
    }, {
      image: congye4
    }, {
      image: congye5
    }]
    console.log('imgList', imgList)
  } else if (dataSetType.value === 'serviceEnterpriseResources') {
    imgList.value = [{
      image: fuwu1,
      name: '团餐经营资质等级认证'
    }, {
      image: fuwu2,
      name: '团餐经营资质等级认证'
    }, {
      image: fuwu3,
      name: '团餐经营资质等级认证'
    }]
    console.log('imgList', imgList)
  } else if (dataSetType.value === 'foodDeliveryEnterpriseQualification') {
    imgList.value = [{
      image: peisong1,
      name: '团餐经营资质等级认证'
    }, {
      image: peisong2,
      name: '团餐经营资质等级认证'
    }, {
      image: peisong3,
      name: '团餐经营资质等级认证'
    }]
    console.log('imgList', imgList)
  } else if (dataSetType.value === 'foodTraceability') {
    imgList.value = [{
      image: suyuan1,
      name: '团餐经营资质等级认证'
    }, {
      image: suyuan2,
      name: '团餐经营资质等级认证'
    }, {
      image: suyuan3,
      name: '团餐经营资质等级认证'
    }, {
      image: suyuan4,
      name: '团餐经营资质等级认证'
    }]
    console.log('imgList', imgList)
  } else {
    // 获取数据
    let type = props.chartConfig.option.dataType
    dataSetType.value = type
    // getDataByType()
    console.log("res", type);
  }
}

// dataset 无法变更条数的补丁
watch(
  () => props.chartConfig.option,
  (newData: any) => {
    console.log("newData 哈哈哈哈", newData);
    // 组件数据变化
    if (newData && newData) {
      option.value = cloneDeep(newData)
    }
  },
  {
    immediate: true,
    deep: true
  }
)

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.value.selectValue) {
        option.value.selectValue = newData.value
        initData()
      } else if (option.value.selectValue !== newData.value) {
        option.value.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)



onMounted(() => {
  console.log("onMounted", option.value);
  initData()
})

</script>

<style scoped lang="scss">

.carouse-img {
  @include deep() {
    .n-carousel__dot--active {
      background: #26BDFF !important;
    }
  }
  .carouse-list {
    border-left: 1px solid #082F50;
    border-right: 1px solid #082F50;
    border-bottom: 1px solid #082F50;
    .carousel-txt{
      margin:5px auto;
      text-align: center;
      position: absolute;
      color: #26BDFF;
      font-size: 16px;
    }

    .carousel-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border:2px solid #082F50;
      padding :3px ;
    }
  }

  .live-right {
    width: 380px;
    height: 200px;
    padding: 1px 1px;
  }

  .camera-box {
    width: 100%;
    height: 100%;
  }
  .custom-dots {
    display: flex;
    margin: 0;
    padding: 0;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 20px;
  }

  .custom-dots li {
    display: inline-block;
    width: 25px;
    height: 4px;
    margin: 0 3px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.4);
    transition:
      width 0.3s,
      background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
  }

  .custom-dots li.is-active {
    width: 30px;
    background: #fff;
  }
}
</style>