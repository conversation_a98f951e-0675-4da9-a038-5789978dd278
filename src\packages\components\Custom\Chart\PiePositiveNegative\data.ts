// 膳食大屏 --------------------- start
// 收入分支数据
export const DIET_LABEL_LIST = [
    {
      name: '储值消费',
      value: 0,
      class: 'skyBlue',
      key: 'cz_price'
    },
    {
      name: '补贴消费',
      value: 0,
      class: 'purple',
      key: 'bt_price'
    },
    {
      name: '第三方消费',
      value: 0,
      class: 'brightcyan',
      key: 'ds_price'
    },
    {
      name: '财政补贴',
      value: 0,
      class: 'yellow',
      key: 'bz_price'
    },
    {
      name: '公益捐赠',
      value: 0,
      class: 'darkblue',
      key: 'gy_price'
    },
    {
      name: '非营业性-其他',
      value: 0,
      class: 'grey',
      key: 'nqt_price'
    },
    {
      name: '营业性-其他',
      value: 0,
      class: 'green',
      key: 'qt_price'
    }
]
// chartOptionData
export const DIET_OPTION_DATA = [
  { value: 0, name: '储值消费', data_type: 'cz_price', itemStyle: { color:'#38B2FF' }, label: { position: 'top' }},
  { value: 0, name: '补贴消费', data_type: 'bt_price',itemStyle: { color:'#6456FF' }, label: { position: 'top' }},
  { value: 0, name: '第三方消费', data_type: 'ds_price', itemStyle: { color:'#0ADDE8' }, label: { position: 'top' }},
  { value: 0, name: '财政补贴', data_type: 'bz_price', itemStyle: { color:'#FFD364' }, label: { position: 'top' }},
  { value: 0, name: '公益捐赠', data_type: 'gy_price', itemStyle: { color:'#23588D' }, label: { position: 'top' }},
  { value: 0, name: '非营业性-其他', data_type: 'nqt_price', itemStyle: { color:'#5E87B5' }, label: { position: 'top' }},
  { value: 0, name: '营业性-其他', data_type: 'qt_price', itemStyle: { color:'#17E9AD' }, label: { position: 'top' }}
]
// 膳食大屏 --------------------- end