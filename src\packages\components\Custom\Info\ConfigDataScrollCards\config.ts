import { PublicConfigClass } from '@/packages/public'
import { ConfigDataScrollCards } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import { chartInitConfig } from '@/settings/designSetting'

// 数据配置
export const option = {
  titleValue: '可配置数据卡片',
  cardDataType: 'custom', // 卡片类型
  isScroll: false, // 是否滚动，默认否
  dataset: [],
  cardConfig: {} // 卡片配置，由config.vue自动设置
}

export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = ConfigDataScrollCards.key
  public chartConfig = cloneDeep(ConfigDataScrollCards)
  public attr = { ...chartInitConfig, w: 288, h: 178, zIndex: -1 }
  // 图表配置项
  public option = cloneDeep(option)
}

export const includes = ['legend', 'xAxis', 'yAxis', 'grid']
