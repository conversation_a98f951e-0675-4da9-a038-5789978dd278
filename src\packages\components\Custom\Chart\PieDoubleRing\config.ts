import { PublicConfigClass } from '@/packages/public'
import { PieDoubleRing } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
// import peropleImage from '@/assets/images/chart/custom/ic_green_people.png'
export const includes = ['legend', 'xAxis', 'yAxis', 'grid']
import { chartInitConfig } from '@/settings/designSetting'
// import  res  from './data.json'

// 数据配置
let pass_rate = 70 // 外环圈进度
let average = 60 // 内环圈进度
let total = 100
export const option = {
  titleValue: '食堂负债情况',
  // dataList: res.week,
  tableDataType: 'mealExpensesScreen',
  chartOpts: {
    // 提示
    tooltip: {
      show: false,
      trigger: 'item',
      formatter: (params) => {
        // 自定义格式化函数
        const data = params.data;
        if (data.value === 0 || data.itemStyle?.color === 'transparent') {
          return null; // 不显示透明部分的tooltip
        }
        return `${params.seriesName}<br/>${params.marker} ${params.data.name || ''}: ${params.data.value}%`;
      },
      // position: ['50%', '50%'], // 提示位置
      // backgroundColor: 'rgba(0,0,0,0.8)',
      // borderColor: '#535353',
      // borderWidth: 1,
      // padding: [8, 12],
      // textStyle: {
      //   color: '#FFFFFF',
      //   fontSize: 14,
      //   fontFamily: 'Microsoft YaHei'
      // },
      // extraCssText: 'box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);' // 阴影效果
    },
    // color: ['#0BF9FE', '#5B99FF'], // 外环颜色 ，内环颜色
    graphic: [
      {
        type: 'text',
        left: 'center',
        top: 'center',
        style: {
          text: [
            '{a|￥78940}',
            '{b|全部负债}'
          ].join('\n'),
          textAlign: 'center',
          textVerticalAlign: 'middle',
          rich: {
            a: {
              fontSize: 28,
              fontWeight: 'bold',
              fontFamily: 'Microsoft YaHei',
              fill: 'rgb(255,255,255)',
              lineHeight: 40
            },
            b: {
              fontSize: 18,
              fontFamily: 'Microsoft YaHei',
              fill: 'rgb(102,102,102)',
              lineHeight: 30
            }
          }
        }
      }
    ],
    series: [
      // 外环白色背景
      {
        type: 'pie',
        radius: ['75%', '85%'],  // 外环：宽度5% (55%-50%)
        silent: true, // 不响应交互
        label: { show: false },
        labelLine: { show: false },
        itemStyle: {
          color: 'rgba(255, 255, 255, 0.1)', // 添加透明度(0.1)
          borderWidth: 0,
          borderRadius: 10 // 圆角大小
        },
        data: [{ value: 100 }]
      },
      // 外环进度
      {
        name: '非流动负债',
        type: 'pie',
        radius: ['75%', '85%'],  // 外环：宽度5% (55%-50%)
        startAngle: 270, // 从顶部开始（90度）
        label: { show: false },
        labelLine: { show: false },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        data: [
          {
            value: 90,
            itemStyle: {
              color: '#0BF9FE', // 外环颜色
              borderRadius: 10 // 圆角大小
            }
          },
          {
            value: 10,
            itemStyle: {
              color: 'transparent'
            }
          }
        ]
      },
      // 内环白色背景
      {
        type: 'pie',
        radius: ['55%', '65%'],  // 内环：宽度5% (40%-35%)
        silent: true,
        label: { show: false },
        labelLine: { show: false },
        itemStyle: {
          color: 'rgba(255, 255, 255, 0.1)', // 添加透明度(0.1)
          borderWidth: 0,
          borderRadius: 10 // 圆角大小
        },
        data: [{ value: 100 }]
      },
      // 内环进度
      {
        name: '流动负债',
        type: 'pie',
        radius: ['55%', '65%'], // 与内环背景一致
        startAngle: 270, // 从顶部开始（270度）
        label: { show: false },
        labelLine: { show: false },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        data: [
          {
            // name: '平均值' + average + '%',
            value: 60,
            itemStyle: {
              color: '#5B99FF', // 内环颜色
              borderRadius: 10 // 圆角大小
            }
          },
          {
            value: 40,
            itemStyle: {
              color: 'transparent'
            }
          }
        ]
      }
    ]
  }
}
// 导出配置
export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = PieDoubleRing.key
  public chartConfig = cloneDeep(PieDoubleRing)
  public attr = { ...chartInitConfig, w: 596, h: 440, zIndex: -1 }
  // 图表配置项
  public option = cloneDeep(option)
}
