# HLS视频监控组件说明

## 支持的视频流格式

### **HLS (.m3u8)** - 唯一支持格式
- **兼容性**: 所有现代浏览器（需要HLS.js库）
- **优势**: 
  - 自适应码率
  - 良好的网络适应性
  - 支持加密
  - 跨平台兼容性好
- **使用场景**: 直播、点播、监控

## 不支持的格式

### 1. **RTMP**
- 浏览器不支持，需要Flash插件
- 建议转换为HLS

### 2. **RTSP**
- 浏览器不支持，需要特殊插件
- 建议转换为HLS

### 3. **HTTP-FLV**
- 需要flv.js库支持
- 建议转换为HLS

### 4. **WebRTC**
- 需要特殊实现
- 建议转换为HLS

## 优化特性

### 1. **HLS专用播放**
- 专门针对HLS格式优化
- 自动检测HLS支持
- Safari原生支持，无需额外库

### 2. **动态加载**
- HLS.js 库按需动态加载
- 减少初始包体积

### 3. **错误处理**
- 播放失败时自动重试
- 详细的错误日志

### 4. **资源管理**
- 组件销毁时自动清理播放器实例
- 防止内存泄漏

## 安装依赖

```bash
npm install hls.js
# 或
yarn add hls.js
```

## 使用示例

```vue
<template>
  <MonitorVideo 
    :chartConfig="chartConfig"
    :themeSetting="themeSetting"
    :themeColor="themeColor"
  />
</template>

<script setup>
import { MonitorVideo } from '@/packages/components/Custom/Monitor/MonitorVideo'

const chartConfig = {
  option: {
    selectValue: 1,
    titleValue: '视频监控'
  }
}
</script>
```

## 后端数据格式

```javascript
{
  code: 0,
  data: [
    {
      id: "camera_001",
      name: "摄像头1",
      address: {
        hls: "https://example.com/live/stream.m3u8?token=xxx"
      }
    }
  ]
}
```

## 性能优化建议

### 1. **CDN加速**
- 将HLS流部署到CDN
- 减少网络延迟

### 2. **自适应码率**
- HLS天然支持自适应码率
- 根据网络状况自动调整

### 3. **预加载策略**
- 设置合适的preload值
- 避免过度预加载

### 4. **错误重试**
- 实现指数退避重试
- 避免频繁重试

## 常见问题

### 1. **视频无法播放**
- 检查网络连接
- 确认token是否有效
- 查看浏览器控制台错误
- 确认HLS.js库是否正确加载

### 2. **延迟过高**
- 优化HLS分片大小
- 减少网络延迟
- 使用低延迟HLS配置

### 3. **内存占用过高**
- 及时销毁播放器实例
- 限制同时播放的视频数量

## 浏览器兼容性

| 浏览器 | HLS支持 |
|--------|---------|
| Chrome | ✅ (需要HLS.js) |
| Firefox | ✅ (需要HLS.js) |
| Safari | ✅ (原生支持) |
| Edge | ✅ (需要HLS.js) |
| IE11 | ❌ |

注意：Safari原生支持HLS，无需HLS.js库。 