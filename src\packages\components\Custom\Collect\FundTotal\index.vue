<template>
  <div :style="`width:${w}px;height:${h}px;`" class="fund-total c-white" style=" {
    background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
    padding: 20px;
    box-sizing: border-box;
    }">
    <div class="content ps-flex row-between col-center">
      <div v-for="(item, index) in totalData.value" :key="index" class="ps-flex row-between w-33p" style="
        display: flex;
        justify-content: space-between;
        width: 30%;
      ">
        <div class="total-item">
          <div class="title" style="
            font-size: 22px;
            font-weight: bold;
            text-align: center;
          ">{{ item.title }}</div>
          <!-- <div class="price" :class="[index===1?'yellow':'green']" style="
            font-size: 36px;
            font-weight: bold;
          ">{{item.fee}}</div> -->
          <div class="price" v-if="index === 1" style="
            color: #FFC73A;
            font-size: 36px;
            font-weight: bold;
            text-align: center;
          ">{{ item.fee }}</div>
          <div class="price" v-else style="
            color: #0BF9FE;
            font-size: 36px;
            font-weight: bold;
            text-align: center;
          ">{{ item.fee }}</div>
          <div class="f-s-14" style="
            font-size: 14px;
            text-align: center;
          ">
            <span class="grey" style="
              color: #FFFFFF99;
            ">月环比</span>
            <span class="m-l-5 m-r-5" style="
              margin: 0px 5px;
            ">{{ item.basis }}</span>
            <img v-if="item.value > 0" src="@/assets/images/chart/custom/up_icon.png" alt="" srcset="">
            <img v-if="item.value < 0" src="@/assets/images/chart/custom/down_icon.png" alt="" srcset="">
          </div>
        </div>
        <div v-if="index < 2" class="line" style="
          border: 1px solid #003C60;
          height: 90px;
        "></div>
      </div>

      <!-- <div class="total-item">
        <div class="title">本月支出</div>
        <div class="price yellow">12032651</div>
        <div class="f-s-14">
          <span class="grey">月环比</span>
          <span class="m-l-5 m-r-5">1.15%</span>
          <img src="@/assets/images/chart/custom/up_icon.png" alt="" srcset="">
        </div>
      </div>
      <div class="line"></div>
      <div class="total-item">
        <div class="title">本月利润</div>
        <div class="price green">620008</div>
        <div class="f-s-14">
          <span class="grey">月环比</span>
          <span class="m-l-5 m-r-5">-1.72%</span>
          <img src="@/assets/images/chart/custom/down_icon.png" alt="" srcset="">
        </div>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { isPreview } from '@/utils'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import refundImage from '@/assets/images/chart/custom/ic_green_refund.png'
import checkImage from '@/assets/images/chart/custom/ic_green_check.png'
import personImage from '@/assets/images/chart/custom/ic_green_person.png'
import { apiGetMonthTotalDetail, apiGetCanteenMonthRevenueExpendProfitList, apiGetMonthCaiwuTotalDetail } from '@/api/path'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { useRoute } from 'vue-router'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

const route = useRoute()
const chartEditStore = useChartEditStore()

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);
// 配置
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option),
  channelId: Number(route.query.channel_id),
  orgId: Number(route.query.org_id),
  selectValue: 0
})
console.log("option", option);

const totalData = reactive({
  value: Array<any>()
})

totalData.value = [{
  title: "本月营收",
  fee: 0,
  basis: 0,
}, {
  title: "本月支出",
  fee: 0,
  basis: 0,
}, {
  title: "本月利润",
  fee: 0,
  basis: 0,
}]

// 获取数据
const getTableData = async () => {
  let api: any
  let params: any = {}
  if (option.value.tableDataType === 'fundData' && option.selectValue) {
    api = apiGetMonthTotalDetail
    // params.channel_id = option.channelId
    params.channel_id = option.selectValue ? option.selectValue : option.channelId
    params.org_list = option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : []
    if (route.query.role_id) {
      params.role_id = Number(route.query.role_id)
    }
  } else if (option.value.tableDataType === 'canteenData') {
    api = apiGetCanteenMonthRevenueExpendProfitList
    params.org_id = option.selectValue ? option.selectValue : option.orgId
  } else if (option.value.tableDataType === 'financeData' && option.selectValue) {
    api = apiGetMonthCaiwuTotalDetail
    // params.channel_id = option.channelId
    params.channel_id = option.selectValue ? option.selectValue : option.channelId
    params.org_list = option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : []
  }
  if (!api) {
    return
  }
  const res = await api(params)
  if (res && res.data) {
    totalData.value = []
    let consumePriceChain = res.data && res.data.consume_price_chain || []
    let profitChain = res.data && res.data.profit_chain || []
    if (consumePriceChain && profitChain && (option.value.tableDataType === 'fundData' || option.value.tableDataType === 'canteenData'|| option.value.tableDataType === 'financeData')) {
      totalData.value.push({
        title: "本月营收",
        fee: (res.data.consume_price > 100000 || res.data.consume_price < -100000) ? (res.data.consume_price / 10000).toFixed(0) + '万' : res.data.consume_price,
        basis: res.data.consume_price_chain,
        value: Number(res.data.consume_price_chain.slice(0, res.data.consume_price_chain.length - 1))
      }, {
        title: "本月支出",
        fee: (res.data.refund_price > 100000 || res.data.refund_price < -100000) ? (res.data.refund_price / 10000).toFixed(0) + '万' : res.data.refund_price,
        basis: res.data.refund_price_chain,
        value: Number(res.data.refund_price_chain.slice(0, res.data.refund_price_chain.length - 1))
      }, {
        title: "本月利润",
        fee: (res.data.profit > 100000 || res.data.profit < -100000) ? (res.data.profit / 10000).toFixed(0) + '万' : res.data.profit,
        basis: res.data.profit_chain,
        value: Number(res.data.profit_chain.slice(0, res.data.profit_chain.length - 1))
      })
    }
  }
}

const selectValue = chartEditStore.getControlGlobalConfigByKey('ChooseSelect')

const canteenAllFundTotal = {
  fanyingyizhong: [{
    title: "本月营收",
    fee: "98748",
    basis: "44.92%",
    type: "up",
  }, {
    title: "本月支出",
    fee: "91835.64",
    basis: "45.44%",
    type: "up",
  }, {
    title: "本月利润",
    fee: "6912.36",
    basis: "38.32%",
    type: "up",
  }],
  fanyingerzhong: [{
    title: "本月营收",
    fee: "94330",
    basis: "46.59%",
    type: "up",
  }, {
    title: "本月支出",
    fee: "87579.9",
    basis: "46.37%",
    type: "up",
  }, {
    title: "本月利润",
    fee: "6750.1",
    basis: "49.49%",
    type: "up",
  }],
  shicaoyizhong: [{
    title: "本月营收",
    fee: "84684",
    basis: "47.62%",
    type: "up",
  }, {
    title: "本月支出",
    fee: "78762.12",
    basis: "47.79%",
    type: "up",
  }, {
    title: "本月利润",
    fee: "5921.88",
    basis: "45.38%",
    type: "up",
  }]
}

// 页面加载
onMounted(() => {
  if (option.value.tableDataType === 'fundData' ||
    option.value.tableDataType === 'canteenData' ||
    option.value.tableDataType === 'financeData') {
    getTableData()
  } else {
    totalData.value = option.value.dataset
  }
})
// 初始化数据
const initData = () => {
  console.log("initData 111111");
  if (option.value.tableDataType === 'fundData' ||
    option.value.tableDataType === 'canteenData' ||
    option.value.tableDataType === 'financeData') {
    getTableData()
  }
}

// dataset 无法变更条数的补丁
watch(
  () => props.chartConfig.option,
  (newData: any) => {
    console.log("newData有新得值更新", newData);
    option.value = cloneDeep(newData)
  },
  {
    immediate: true,
    deep: true
  }
)

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

</script>

<style scoped lang="scss">
.fund-total {

  // background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
  // border: 1px solid;
  // border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
  // padding: 20px;
  // box-sizing: border-box;
  .w-33p {
    width: 28%;
  }

  .title {
    font-size: 22px;
    font-weight: bold;
  }

  .price {
    font-size: 36px;
    font-weight: bold;
  }

  .f-s-14 {
    font-size: 14px;
  }

  .grey {
    color: #FFFFFF99;
  }

  .green {
    color: #0BF9FE;
  }

  .yellow {
    color: #FFC73A;
  }

  .line {
    border: 1px solid #003C60;
    height: 90px;
  }
}
</style>