<template>
  <div :style="`width:${w}px;height:${h}px;`" class="fund-data-table c-white">
    <!--标题-->
    <fund-head v-if="option.value.tableDataType !== 'mealInformation'" :title="option.value.titleValue"
      :type="option.value.tableDataType" :time-type="option.dateType" :style="`width:${w}px`"
      @select-handle="selectHandle">
    </fund-head>
    <!--内容-->
    <div class="table-wrap" :style="`height:${h - 75}px;`" style="
      background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
      border: 1px solid;
      border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
      padding: 15px 20px 10px;
      overflow: hidden;
    ">
      <div v-if="option.value.tableDataType === 'canteenConsumption'" class="ps-flex col-center m-b-10">
        <img src="@/assets/images/chart/custom/total_consumption.png" alt="" srcset="">
        <div :style="{
          fontSize: '28px',
          marginLeft: '10px'
        }">
          消费总金额：
          <span :style="{
            color: '#26BDFF',
            fontWeight: 600
          }">￥{{ option.totalPrice }}</span>
        </div>
      </div>
      <div v-if="option.value.tableDataType === 'mealInformation'" class="meal-info-header ps-flex col-center">
        <img src="@/assets/images/chart/custom/meal_info_icon.png" alt="" srcset="">
        <div class="text">{{ option.value.titleValue }}</div>
      </div>
      <div v-if="option.value.tableDataType !== 'incomeRanking'" class="table-header" style="
        position: relative;
        width: 555px; /* 设置你的div宽度 */
        height: 36px; /* 设置你的div高度 */
        overflow: hidden; /* 确保伪元素不会溢出div */
        font-size: 17px;
        margin-bottom: 5px;
        border: 1px solid #02A9FF66;
        background: #04253D;
        padding: 0 5px;
        box-sizing: border-box;
      ">
        <div class="table-header-top"></div>
        <div class="table-header-bottom"></div>
        <div v-if="option.value.tableDataType === 'profit'" class="ps-flex row-between" style="
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">学校名称</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">总收入</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">总支出</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">总利润</div>
          <div class="item-title flex-2" style="
            flex: 2;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">利润率</div>
        </div>
        <div v-if="option.value.tableDataType === 'perCapita'" class="ps-flex row-between" style="
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div class="item-title flex-2" style="
            flex: 2;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">序号</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">学校名称</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">消费总金额</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">人均消费金额</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">人均消费月环比</div>
        </div>
        <div v-if="option.value.tableDataType === 'costExpenditureDetails'" class="ps-flex row-between" style="
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">学校名称</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">原材料成本</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">人工成本</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">水电气费用</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">营业-其他</div>
          <div class="item-title flex-3" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">非营业-其他</div>
        </div>
        <div v-if="option.value.tableDataType === 'proportionOfRawMaterial'" class="ps-flex row-between" style="
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div class="item-title flex-2" style="
            flex: 2;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">序号</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">学校名称</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">出库次数</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">出库金额</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">占总支出</div>
        </div>
        <div v-if="option.value.tableDataType === 'canteenConsumption'" class="ps-flex row-between" style="
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div class="item-title flex-2" style="
            flex: 2;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">序号</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">日期</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">消费金额</div>
        </div>
        <div v-if="option.value.tableDataType === 'purchaseDetails'" class="ps-flex row-between" style="
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div class="item-title flex-2" style="
            flex: 2;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">序号</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">采购日期</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">采购金额</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">所属仓库</div>
        </div>
        <div v-if="option.value.tableDataType === 'InventoryDetails'" class="ps-flex row-between" style="
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div class="item-title flex-2" style="
            flex: 2;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">序号</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">所属仓库</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">材料名称</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">平均入库价</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">入库数量</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">出库数量</div>
        </div>
        <div
          v-if="option.value.tableDataType === 'incomeSituation' || option.value.tableDataType === 'incomeSituation-month' || option.value.tableDataType === 'incomeSituation-year'"
          class="ps-flex row-between" style="
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div class="item-title flex-1" style="
            flex: 1;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">序号</div>
          <div class="item-title flex-2" style="
            flex: 2;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">类型</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">项目</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">金额</div>
        </div>
        <div v-if="option.value.tableDataType === 'debtSituation'" class="ps-flex row-between" style="
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div class="item-title flex-2" style="
            flex: 2;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">负债类别</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">负载类型</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">负债金额</div>
        </div>
        <div v-if="option.value.tableDataType === 'healthCertificate'" class="ps-flex row-between" style="
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div class="item-title flex-1" style="
            flex: 1;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">序号</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">证件</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">姓名</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">身份证编号</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">截至有效期</div>
        </div>
        <div v-if="option.value.tableDataType === 'todayInspection'" class="ps-flex row-between" style="
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div class="item-title flex-1" style="
            flex: 1;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">序号</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">巡查项目</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">巡查人员</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">巡查时间</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">巡查结果</div>
        </div>
        <div v-if="option.value.tableDataType === 'foodInformation'" class="ps-flex row-between" style="
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div class="item-title flex-1" style="
            flex: 1;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">序号</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">食材名称</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">供应商名称</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">价格（元/斤）</div>
        </div>
        <div v-if="option.value.tableDataType === 'mealInformation'" class="ps-flex row-between" style="
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div class="item-title flex-1" style="
            flex: 1;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">序号</div>
          <div class="item-title flex-4" style="
            flex: 4;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">单位名称</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">供餐餐段</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">供餐数量</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">适用年龄</div>
          <div class="item-title flex-3" style="
            flex: 3;
            height: 36px;
            line-height: 36px;
            color: #26BDFF;
            text-align: center;
          ">收费标准</div>
        </div>
      </div>
      <div v-if="option.value.tableDataType === 'incomeRanking'" class="income-ranking">
        <img src="@/assets/images/chart/custom/income_ranking.png" style="width: 84px; height: 48px;">
        <span class="income-ranking-label">经营收入汇总：</span>
        <span class="income-ranking-value">{{ incomeRankingText }}</span>
      </div>
      <div class="scroll-wrap"
        :style="`height:${option.value.scrollHeight ? option.value.scrollHeight : 305}px;overflow: hidden;`">
        <vue3-seamless-scroll class="scroll" v-model='option.value.isScroll' :list="totalData.value" :step="0.5"
          :hover="true" :limit-scroll-num="totalData.limiScrollNum" :wheel="true">
          <div v-if="option.value.tableDataType !== 'incomeRanking'">
            <div class='tag-item' v-for="(item, index) in totalData.value" :key="index" style="
                min-width: 479px;
                min-height: 40px;
              ">
              <div v-if="option.value.tableDataType === 'profit'" class="ps-flex flex-align-center p-l-5 p-r-5" style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0px 5px;
                ">
                <!-- <div :class="[index < 2 ? 'green-color' : '']" class="item-info flex-4" style=" -->
                <div class="item-info flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.organization_name }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">￥{{ item.in_price }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">￥{{ item.out_price }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">￥{{ item.profit }}</div>
                <div
                  :class="[((item.profit_value > item.profit_rate_dict.surplus_val) || (item.profit_value < item.profit_rate_dict.loss_val)) ? 'red-color' : '']"
                  class="item-info flex-2" style="
                    flex: 2;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.profit_rate }}</div>
              </div>
              <div v-if="option.value.tableDataType === 'perCapita'" class="ps-flex flex-align-center p-l-5 p-r-5" style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0px 5px;
                ">
                <div class="item-info flex-2" style="
                    flex: 2;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ index + 1 }}</div>
                <div class="item-info flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.organization_name }}</div>
                <div class="item-info flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">￥{{ item.consume_price }}</div>
                <div class="item-info flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">￥{{ item.average_consume_price }}</div>
                <div class="item-info flex-4" :class="[item.value > 0 ? 'red-color' : 'green2-color']" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">
                  {{ item.chain }}
                  <img v-if="item.value > 0" src="@/assets/images/chart/custom/up_icon.png" alt="" srcset="">
                  <img v-if="item.value < 0" src="@/assets/images/chart/custom/down_icon.png" alt="" srcset="">
                </div>
              </div>
              <div v-if="option.value.tableDataType === 'costExpenditureDetails'"
                class="ps-flex flex-align-center p-l-5 p-r-5" style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0px 5px;
                ">
                <!-- <div :class="[index < 2 ? 'green-color' : '']" class="item-info flex-4" style=" -->
                <div class="item-info flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.organization_name }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">￥{{ item.raw_material_cost }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">￥{{ item.labor_cost }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">￥{{ item.utilities }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">￥{{ item.operating_other_costs }}</div>
                <div class="item-info flex-3" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">￥{{ item.non_operating_other_costs }}</div>
              </div>
              <div v-if="option.value.tableDataType === 'proportionOfRawMaterial'"
                class="ps-flex flex-align-center p-l-5 p-r-5" style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0px 5px;
                ">
                <div class="item-info flex-2" style="
                    flex: 2;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ index + 1 }}</div>
                <div class="item-info flex-4" :class="[item.canteen_type === 'ziying' ? 'blue-color' : 'yellow-color']"
                  style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.organization_name }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.refund_num }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">￥{{ item.in_price }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.percentage }}</div>
              </div>
              <div v-if="option.value.tableDataType === 'canteenConsumption'"
                class="ps-flex flex-align-center p-l-5 p-r-5" style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0px 5px;
                ">
                <div class="item-info flex-2" style="
                    flex: 2;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ index + 1 }}</div>
                <div class="item-info flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ (item.date) }}（{{ item.week_str }}）</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">
                  <span v-if="item.consume_price !== '--'">￥</span>
                  {{ item.consume_price }}
                </div>
              </div>
              <div v-if="option.value.tableDataType === 'purchaseDetails'" class="ps-flex flex-align-center p-l-5 p-r-5"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0px 5px;
                ">
                <div class="item-info flex-2" style="
                    flex: 2;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ index + 1 }}</div>
                <div class="item-info flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ (item.data_str) }}</div>
                <div class="item-info flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">￥{{ item.consume_price }}</div>
                <div class="item-info flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.warehouse_name }}</div>
              </div>
              <div v-if="option.value.tableDataType === 'InventoryDetails'" class="ps-flex flex-align-center p-l-5 p-r-5"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0px 5px;
                ">
                <div class="item-info flex-2" style="
                    flex: 2;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ index + 1 }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ (item.name) }}</div>
                <div class="item-info flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.materials_name }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">￥{{ item.average }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.entry_num }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.exist_num }}</div>
              </div>
              <div
                v-if="option.value.tableDataType === 'incomeSituation' || option.value.tableDataType === 'incomeSituation-month' || option.value.tableDataType === 'incomeSituation-year'"
                class="ps-flex flex-align-center p-l-5 p-r-5" style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0px 5px;
                ">
                <div class="item-info flex-1" style="
                    flex: 1;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ index + 1 }}</div>
                <div class="item-info flex-2" style="
                    flex: 2;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ (item.flow_type) }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.data_type }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.price }}</div>
              </div>
              <div v-if="option.value.tableDataType === 'debtSituation'" class="ps-flex flex-align-center p-l-5 p-r-5"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0px 5px;
                ">
                <div class="item-info flex-2" style="
                    flex: 2;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ (item.liability_category_alias) }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.liability_type_alias }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.price }}</div>
              </div>
              <div v-if="option.value.tableDataType === 'healthCertificate'" class="ps-flex flex-align-center p-l-5 p-r-5"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0px 5px;
                ">
                <div class="item-info health-item-img flex-1" style="
                    flex: 1;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    height: 80px!important;
                    line-height: 80px!important;
                  ">{{ index + 1 }}</div>
                <div class="item-info health-item-img flex-3" style="
                    padding: 10px 0;
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    height: 80px!important;
                    line-height: 80px!important;
                  ">
                  <img :src="item.health_image" alt="" srcset="" style="height: 80px!important;">
                </div>
                <div class="item-info health-item-img flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    height: 80px!important;
                    line-height: 80px!important;
                  ">{{ item.name }}</div>
                <div class="item-info health-item-img flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    height: 80px!important;
                    line-height: 80px!important;
                  ">{{ (item.id_number) }}</div>
                <div class="item-info health-item-img flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    height: 80px!important;
                    line-height: 80px!important;
                  ">{{ item.effective_time }}</div>
              </div>
              <div v-if="option.value.tableDataType === 'todayInspection'" class="ps-flex flex-align-center p-l-5 p-r-5"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0px 5px;
                ">
                <div class="item-info flex-1" style="
                    flex: 1;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ index + 1 }}</div>
                <div class="item-info flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.type }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.person }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ (item.time) }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.result }}</div>
              </div>
              <div v-if="option.value.tableDataType === 'foodInformation'" class="ps-flex flex-align-center p-l-5 p-r-5"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0px 5px;
                ">
                <div class="item-info flex-1" style="
                    flex: 1;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  ">{{ index + 1 }}</div>
                <div class="item-info flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  ">{{ item.name }}</div>
                <div class="item-info flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  ">{{ item.supplier_manage_name }}</div>
                <div class="item-info flex-4" style="
                    flex: 4;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  ">￥{{ item.price }}</div>
              </div>
              <div v-if="option.value.tableDataType === 'mealInformation'" class="ps-flex flex-align-center p-l-5 p-r-5"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 0px 5px;
                ">
                <div class="item-info flex-1" style="
                    flex: 1;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ index + 1 }}</div>
                <div class="item-info flex-4" style="
                    flex: ;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.name }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ item.meal }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ (item.num) }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">{{ (item.age) }}</div>
                <div class="item-info flex-3" style="
                    flex: 3;
                    height: 40px;
                    line-height: 40px;
                    color: #FFF;
                    text-align: center;
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    
                  ">￥{{ (item.fee) }}</div>
              </div>
            </div>
          </div>
          <div v-else>
            <div v-for="(item, index) in totalData.value" :key="index" class="income-ranking-table">
              <div class="income-ranking-table-no">
                <img src="@/assets/images/chart/custom/No1.png" v-if="index === 0" style="width: 30px; height: 30px;">
                <img src="@/assets/images/chart/custom/No2.png" v-else-if="index === 1" style="width: 30px; height: 30px;">
                <img src="@/assets/images/chart/custom/No3.png" v-else-if="index === 2" style="width: 30px; height: 30px;">
                <img src="@/assets/images/chart/custom/otherNo.png" v-else style="width: 30px; height: 30px;">
                <span class="index" v-if="index >= 3">{{index + 1}}</span>
              </div>
              <div class="income-ranking-table-line" style="width: 430px;">
                <div class="income-ranking-table-line-name">{{ item.org_name }}</div>
                <div class="income-ranking-table-line-price">￥{{ parseFloat(item.price).toFixed(2) }}</div>
                <div :class="['income-ranking-table-line-color', index === 0 ? 'first' : '', index === 1 ? 'second' : '', index === 2 ? 'third' : '' ]" :style="{width: `${accDiv(parseFloat(item.price), incomeRankingData)}`, height: '30px'}"></div>
              </div>
              <div :class="['income-ranking-table-rate', item.price_chain >= 0 ? 'green' : 'red']" style="width: 5em; text-align: right;">
                {{ item.price_chain > 0 ? '+' + item.price_chain.toFixed(2) + '%' : item.price_chain.toFixed(2) + '%' }}
              </div>
            </div>
          </div>
        </vue3-seamless-scroll>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { getSevenDateRange, divide } from '@/utils'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'
import FundHead from '@/components/FundHead/index.vue'
// import { apiGetFoodReservedList } from '@/api/path'
import { StorageEnum } from '@/enums/storageEnum'
import { getLocalStorage } from '@/utils'
import { useRoute } from 'vue-router'
import dataJson from './data.json'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import {
  apiGetIncomeProfitRank, apiGetPersonAverageConsumeRank,
  apiGetCostExpendDetails, apiGetRawMaterialsPercentageRank,
  apiGetCanteenConsumeAmountList, apiGetRawMaterialsRankList,
  apiGetRawMaterialsDrpDetailsList, apiBackgroundFundSupervisionBigShieldIngredientInfoList,
  apiBackgroundFundSupervisionBigShieldJopPersonList, apiBackgroundFundSupervisionBigShieldIncome,
  apiBackgroundFundSupervisionBigShieldLiability, apiBackgroundFundSupervisionBigShieldIncomeRankingData
} from '@/api/path'

import fanyingyizhong1 from "@/assets/images/newDemo/jiankang/fanyingyizhong1.png"
import fanyingyizhong2 from "@/assets/images/newDemo/jiankang/fanyingyizhong2.png"
import fanyingyizhong3 from "@/assets/images/newDemo/jiankang/fanyingyizhong3.png"
import fanyingyizhong4 from "@/assets/images/newDemo/jiankang/fanyingyizhong4.png"
import fanyingyizhong5 from "@/assets/images/newDemo/jiankang/fanyingyizhong5.png"
import fanyingerzhong1 from "@/assets/images/newDemo/jiankang/fanyingerzhong1.png"
import fanyingerzhong2 from "@/assets/images/newDemo/jiankang/fanyingerzhong2.png"
import fanyingerzhong3 from "@/assets/images/newDemo/jiankang/fanyingerzhong3.png"
import fanyingerzhong4 from "@/assets/images/newDemo/jiankang/fanyingerzhong4.png"
import fanyingerzhong5 from "@/assets/images/newDemo/jiankang/fanyingerzhong5.png"
import fanyingerzhong6 from "@/assets/images/newDemo/jiankang/fanyingerzhong6.png"
import shicaoyizhong1 from "@/assets/images/newDemo/jiankang/shicaoyizhong1.png"
import shicaoyizhong2 from "@/assets/images/newDemo/jiankang/shicaoyizhong2.png"
import shicaoyizhong3 from "@/assets/images/newDemo/jiankang/shicaoyizhong3.png"
import shicaoyizhong4 from "@/assets/images/newDemo/jiankang/shicaoyizhong4.png"
import shicaoyizhong5 from "@/assets/images/newDemo/jiankang/shicaoyizhong5.png"
const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

const route = useRoute()
const chartEditStore = useChartEditStore()

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);

// 配置
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option),
  totalPrice: 0,
  dateType: 'month',
  channelId: Number(route.query.channel_id),
  orgId: Number(route.query.org_id),
  selectValue: 0
})
console.log("option", option);

const scrollHeight = props.chartConfig.option.scrollHeight ? props.chartConfig.option.scrollHeight : 305


let organizationId
const info = getLocalStorage(StorageEnum.GO_SYSTEM_STORE)
if (info) {
  organizationId = info.userInfo.use_org
} else {
  organizationId = Number(route.query.organization_id);
}

const totalData = reactive({
  // value: option.value.dataset
  value: Array<any>(),
  limiScrollNum: 7
})

// 健康证假数据
const healthCertificateAllData = {
  fanyingyizhong: [
    {
      "name": "王丽娜",
      "number": "410106197505200023",
      "date": "2025/12/9",
      "img": fanyingyizhong1
    },
    {
      "name": "李明华",
      "number": "41010519800101001X",
      "date": "2026/1/3",
      "img": fanyingyizhong2
    },
    {
      "name": "刘俊轩",
      "number": "410181198211110037",
      "date": "2025/12/16",
      "img": fanyingyizhong3
    },
    {
      "name": "周晓琳",
      "number": "410182198803150046",
      "date": "2026/2/2",
      "img": fanyingyizhong4
    },
    {
      "name": "孙浩然",
      "number": "410183199507200054",
      "date": "2026/1/16",
      "img": fanyingyizhong5
    }
  ],
  fanyingerzhong: [
  {
        "name": "李文博",
        "number": "410185199102140070",
        "date": "2025/12/29",
        "img": fanyingerzhong1
    },
    {
        "name": "王雅琪",
        "number": "410184199309300062",
        "date": "2025/12/21",
        "img": fanyingerzhong2
    },
    {
        "name": "陈思颖",
        "number": "410108198706250088",
        "date": "2026/1/29",
        "img": fanyingerzhong3
    },
    {
        "name": "刘浩然",
        "number": "410102199412120095",
        "date": "2025/12/25",
        "img": fanyingerzhong4
    },
    {
        "name": "周志远",
        "number": "410105198107150018",
        "date": "2025/12/30",
        "img": fanyingerzhong5
    },
    {
        "name": "吴子淳",
        "number": "410103199604220023",
        "date": "2026/1/20",
        "img": fanyingerzhong6
    }
  ],
  shicaoyizhong: [
    {
    "name": "尹天佑",
    "number": "410185199302140073",
    "date": "2026/1/29",
    "img": shicaoyizhong1
    },
    {
    "name": "罗梓涵",
    "number": "410108198906250085",
    "date": "2026/1/21",
    "img": shicaoyizhong2
    },
    {
    "name": "汤若琳",
    "number": "410103199804220024",
    "date": "2025/12/27",
    "img": shicaoyizhong3
    },
    {
    "name": "宋一帆",
    "number": "410105198307150016",
    "date": "2025/12/25",
    "img": shicaoyizhong4
    },
    {
    "name": "邹雨馨",
    "number": "410106197808200021",
    "date": "2025/12/18",
    "img": shicaoyizhong5
    }
  ]
}
// 食材信息公示假数据


// 备用
// const foodInformationAllData = {
//   fanyingyizhong: [],
//   fanyingerzhong: [],
//   shicaoyizhong: []
// }
const accDiv = (arg1, arg2) => {
  const [t1, t2] = [arg1, arg2].map(num => 
    (num.toString().split('.')[1] || '').length
  )
  const r1 = Number(arg1.toString().replace('.', ''))
  const r2 = Number(arg2.toString().replace('.', ''))
  return ((r1 / r2) * Math.pow(10, t2 - t1) * 100) + '%'
}

const dateRange = getSevenDateRange(7)

const incomeRankingData = ref<any>(0)
const incomeRankingText = ref<any>('0')

// 获取数据
const getTableData = async (type?: any) => {
  let api
  let params: any = {}
  if (option.value.tableDataType === 'profit') { // 收支利润排行
    api = apiGetIncomeProfitRank
    params.channel_id = option.selectValue ? option.selectValue : option.channelId
    params.date_type = type ? type : option.dateType
    if (route.query.role_id) {
      params.role_id = Number(route.query.role_id)
    }
  } else if (option.value.tableDataType === 'perCapita') { // 人均消费排行-本月
    api = apiGetPersonAverageConsumeRank
    params.channel_id = option.selectValue ? option.selectValue : option.channelId
    if (route.query.role_id) {
      params.role_id = Number(route.query.role_id)
    }
  } else if (option.value.tableDataType === 'costExpenditureDetails') { // 成本支出明细
    api = apiGetCostExpendDetails
    params.channel_id = option.selectValue ? option.selectValue : option.channelId
    params.date_type = type ? type : option.dateType
    if (route.query.role_id) {
      params.role_id = Number(route.query.role_id)
    }
  } else if (option.value.tableDataType === 'proportionOfRawMaterial') { // 原材料占比排行-本月
    api = apiGetRawMaterialsPercentageRank
    params.channel_id = option.selectValue ? option.selectValue : option.channelId
    if (route.query.role_id) {
      params.role_id = Number(route.query.role_id)
    }
  } else if (option.value.tableDataType === 'canteenConsumption') { // 食堂就餐消费金额
    api = apiGetCanteenConsumeAmountList
    params.org_id = option.selectValue ? option.selectValue : option.orgId
    params.date_type = type ? type : option.dateType
  } else if (option.value.tableDataType === 'purchaseDetails') { // 原材料采购明细-本月
    api = apiGetRawMaterialsRankList
    params.org_id = option.selectValue ? option.selectValue : option.orgId
    if (route.query.role_id) {
      params.role_id = Number(route.query.role_id)
    }
  } else if (option.value.tableDataType === 'InventoryDetails') { // 原材料出入库明细-本月
    api = apiGetRawMaterialsDrpDetailsList
    params.org_id = option.selectValue ? option.selectValue : option.orgId
  } else if (option.value.tableDataType === 'foodInformation') {
    api = apiBackgroundFundSupervisionBigShieldIngredientInfoList
    params.org_id = option.selectValue ? option.selectValue : option.orgId
  } else if (option.value.tableDataType === 'healthCertificate') {
    api = apiBackgroundFundSupervisionBigShieldJopPersonList
    params.org_id = option.selectValue ? option.selectValue : option.orgId
  } else if (option.value.tableDataType === 'incomeSituation') { // 收入情况
    api = apiBackgroundFundSupervisionBigShieldIncome
    params.org_list = option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined
    params.channel_id = option.channelId
    params.date_type = type ? type : option.dateType
  } else if (option.value.tableDataType === 'incomeSituation-month') {
    api = apiBackgroundFundSupervisionBigShieldIncome
    params.org_list = option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined
    params.channel_id = option.channelId
    params.date_type = 'month'
  } else if (option.value.tableDataType === 'incomeSituation-year') {
    api = apiBackgroundFundSupervisionBigShieldIncome
    params.org_list = option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined
    params.channel_id = option.channelId
    params.date_type = 'year'
  } else if (option.value.tableDataType === 'debtSituation') {
    api = apiBackgroundFundSupervisionBigShieldLiability
    params.org_list = option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined
    params.channel_id = option.channelId
  } else if (option.value.tableDataType === 'incomeRanking') {
    api = apiBackgroundFundSupervisionBigShieldIncomeRankingData
    params.channel_id = option.selectValue ? option.selectValue : option.channelId
    params.date_type = type ? type : option.dateType
    if (route.query.role_id) {
      params.role_id = Number(route.query.role_id)
    }
  }
  const res = await api(params)


  if (res.code === 0) {
    if (res && res.data) {
      totalData.value = res.data
      if (option.value.tableDataType === 'canteenConsumption') {
        option.totalPrice = 0
        totalData.value.map(item => {
          if (item.consume_price !== '--') {
            option.totalPrice += Number(item.consume_price)
          }
          return item
        })
        option.totalPrice = Number(option.totalPrice.toFixed(2))
      }
      if (option.value.tableDataType === 'perCapita') {
        totalData.value.map(item => {
          item.value = Number(item.chain.slice(0, item.chain.length - 1))
          return item
        })
      }
      if (option.value.tableDataType === 'profit') {
        totalData.value.map(item => {
          item.profit_value = Number(item.profit_rate.slice(0, item.profit_rate.length - 1))
          return item
        })
      }
      if (option.value.tableDataType === 'incomeRanking') {
        let totalPrice = totalData.value.reduce((acc, obj) => {
          const num = parseFloat(obj.price)
          return Math.round(acc + (isNaN(num) ? 0 : num))
        }, 0)
        incomeRankingData.value = divide(totalPrice)
        // console.log('看看汇总', incomeRankingData.value , typeof incomeRankingData.value)
        if (incomeRankingData.value > 1000000) {
          console.log('进到判断了')
          const scaled = incomeRankingData.value / 1000000
          let str = (Math.round((scaled + Number.EPSILON) * 100) / 100) + 'W'
          incomeRankingText.value = str
        } else {
          incomeRankingText.value = '￥' + incomeRankingData.value
        }
        // 排序
        totalData.value.sort((a, b) => {
          // 将字符串转换为数字进行比较
          const priceA = parseFloat(a.price)
          const priceB = parseFloat(b.price)
          
          return priceB - priceA
        })
        totalData.value = totalData.value.map(item => {
          item.price = divide(item.price)
          return item
        })
        console.log('totalData', totalData.value)
      }
      if (option.value.tableDataType === 'foodInformation') {
        console.log('请求了', totalData.value)
        totalData.value.map(item => {
          item.price = Number(formatPrice(item.price))
          return item
        })
      }
      if (option.value.tableDataType === 'healthCertificate') {
        console.log('请求了', totalData.value)
        totalData.limiScrollNum = 3
      }
      if (option.value.tableDataType === 'incomeSituation' || option.value.tableDataType === 'incomeSituation-month' || option.value.tableDataType === 'incomeSituation-year'|| option.value.tableDataType === 'debtSituation') {
        totalData.value = totalData.value.map(item => {
          item.price = '￥' + divide(item.price)
          return item
        })
      }
    }
    console.log("apiBackgroundFundSupervisionBigShieldIncome 123", option.value.tableDataType, totalData.value);
  }
}

const formatPrice = (num: number) => {
  let aaa = divide(num)
  if (aaa >= 1000000) {
    // console.log(Math.round(aaa/10000) + 'w') // floor
    return Math.round(aaa / 10000) + 'w'
  } else {
    return aaa
  }
}

const selectHandle = (type: string) => {
  console.log('看看type', type)
  option.dateType = type
  if (option.value.tableDataType === 'profit' || option.value.tableDataType === 'costExpenditureDetails' ||
    option.value.tableDataType === 'canteenConsumption' || option.value.tableDataType === 'incomeSituation' ||
    option.value.tableDataType === 'incomeRanking'
  ) {
    console.log('进入了', type)
    getTableData(type)
  }
  // if (option.value.tableDataType === 'profit' && type === "month") {
  //   totalData.value = dataJson.profitList.monthData
  // } else if (option.value.tableDataType === 'profit' && type === "year") {
  //   totalData.value = dataJson.profitList.yearData
  // }
  // if (option.value.tableDataType === 'costExpenditureDetails' && type === "month") {
  //   totalData.value = dataJson.costExpenditureDetailsList.monthData
  // } else if (option.value.tableDataType === 'costExpenditureDetails' && type === "year") {
  //   totalData.value = dataJson.costExpenditureDetailsList.yearData
  // }

}
// 初始化
const initData = () => {
  if (option.value.tableDataType === 'canteenConsumption') {
    option.dateType = 'week'
  } else {
    option.dateType = 'month'
  }
  if (option.value.tableDataType === 'profit' ||
    option.value.tableDataType === 'costExpenditureDetails' ||
    option.value.tableDataType === 'proportionOfRawMaterial' ||
    option.value.tableDataType === 'perCapita' ||
    option.value.tableDataType === 'canteenConsumption' ||
    option.value.tableDataType === 'purchaseDetails' ||
    option.value.tableDataType === 'InventoryDetails' ||
    option.value.tableDataType === 'foodInformation' ||
    option.value.tableDataType === 'healthCertificate' ||
    option.value.tableDataType === 'incomeRanking') {
    getTableData(option.dateType)
  } else if ((option.value.tableDataType === 'incomeSituation' ||
    option.value.tableDataType === 'incomeSituation-month' ||
    option.value.tableDataType === 'incomeSituation-year'||
    option.value.tableDataType === 'debtSituation') && option.selectValue) {
    getTableData(option.dateType)
  } else {
    totalData.value = option.value.dataset
  }

}

// 页面加载
onMounted(() => {
  initData()
})

// dataset 无法变更条数的补丁
// watch(
//   () => props.chartConfig.option,
//   (newData: any) => {
//     console.log("newData有新得值更新", newData);
//     option.value = cloneDeep(newData)
//   },
//   {
//     immediate: true,
//     deep: true
//   }
// )

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

</script>

<style scoped lang="scss">
.fund-data-table {
  .meal-info-header {
    margin-bottom: 10px;

    img {
      width: 32px;
    }

    .text {
      color: #0BF9FE;
      font-size: 18px;
      font-style: italic;
      letter-spacing: 2px;
      font-weight: bold;
    }
  }

  .table-wrap {
    // background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
    // border: 1px solid;
    // border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
    // padding: 15px 20px 10px;
    // overflow: hidden;
  }

  .table-header {

    // position: relative;
    // width: 555px; /* 设置你的div宽度 */
    // height: 36px; /* 设置你的div高度 */
    // overflow: hidden; /* 确保伪元素不会溢出div */
    // font-size: 17px;
    // margin-bottom: 5px;
    // border: 1px solid #02A9FF66;
    // background: #04253D;
    // padding: 0 5px;
    // box-sizing: border-box;
    .item-title {
      height: 36px;
      line-height: 36px;
      color: #26BDFF;
      text-align: center;
    }

    .table-header-top::before,
    .table-header-top::after,
    .table-header-bottom::before,
    .table-header-bottom::after {
      content: '';
      position: absolute;
      width: 6px;
      /* 短边框的宽度 */
      height: 6px;
      /* 短边框的高度 */
    }

    .table-header-top::before {
      top: 0;
      right: 0;
      border-top: 2px solid #06A5FF;
      border-right: 2px solid #06A5FF;
    }

    .table-header-top::after {
      top: 0;
      left: 0;
      border-top: 2px solid #06A5FF;
      border-left: 2px solid #06A5FF;
    }

    .table-header-bottom::before {
      bottom: 0;
      right: 0;
      border-bottom: 2px solid #06A5FF;
      border-right: 2px solid #06A5FF;
    }

    .table-header-bottom::after {
      bottom: 0;
      left: 0;
      border-bottom: 2px solid #06A5FF;
      border-left: 2px solid #06A5FF;
    }
  }

  .scroll-wrap {
    // overflow: hidden;
  }

  .scroll {
    /* 必需要设置合适的高,因为他的原理是往列表最后添加列表重复元素，所以这个组件高不要大于其内容原先的最大高度 */
    height: 305px;
    min-width: 450px;
    overflow: hidden;
  }

  .tag-item {

    // min-width: 479px;
    // min-height: 40px;
    .item-info {
      // height: 40px;
      // line-height: 40px;
      // color: #FFF;
      // text-align: center;
      // font-size: 16px;
      // overflow: hidden;
      // text-overflow: ellipsis;
      // white-space: nowrap;
    }

    .health-item-img {

      // height: 80px!important;
      // line-height: 80px!important;
      img {
        // margin-top: 12px;
      }
    }

    .item-img {
      width: 260px;
      height: 178px;
      padding: 5px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .tag-item:nth-child(2n-1) {
    background: #0E48724D;
  }

  .flex-4 {
    flex: 4;
  }

  .flex-3 {
    flex: 3;
  }

  .flex-2 {
    flex: 2;
  }

  .flex-1 {
    flex: 1;
  }

  .green-color {
    color: #0BF9FE !important;
  }

  .green2-color {
    color: #29DC7A !important;
  }

  .red-color {
    color: #FF4343 !important;
  }

  .blue-color {
    color: #26BDFF !important;
  }

  .yellow-color {
    color: #FF9A35 !important;
  }

  .f-s-28 {
    font-size: 28px;
  }

  .f-w-600 {
    font-weight: 600;
  }

  .income-ranking {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-top: 10px;
    &-label {
      color: #FFF;
      font-size: 28px;
      font-weight: 400;
      line-height: 100%; /* 32px */
    }
    &-value {
      color: #26BDFF;
      font-size: 28px;
      font-weight: 700;
      line-height: 100%; /* 40px */
    }
    &-table {
      display: flex;
      align-items: center;
      justify-content: space-around;
      margin: 10px 0px;
      &-no {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        .index {
          position: absolute;
          height: 30px;
          line-height: 30px;
        }
      }
      &-line {
        position: relative;
        background-color: #0E487266;
        &-name {
          position: absolute;
          left: 10px;
          height: 30px;
          line-height: 30px;
        }
        &-price {
          position: absolute;
          right: 10px;
          height: 30px;
          line-height: 30px;
        }
        &-color {
          background-color: #0AA1E3;
        }
        .first {
          background-color: #EBB222;
        }
        .second {
          background-color: #4C6B94;
        }
        .third {
          background-color: #FF703D;
        }
      }
      .green {
        color: #29DC7A;
      }
      .red {
        color: #FF4343;
      }
    }
  }
}
</style>