<template>
  <div :style="`width:${w}px;height:${h}px;`" class="info-announcement c-white">
    <div class="table-wrap">
      <div class="info-item left">
        <div class="info-header ps-flex col-center" >
          <img src="@/assets/images/chart/custom/meal_info_icon.png" alt="" srcset="">
          <div class="text">{{option.value.titleValue}}</div>
        </div>
        <div class="grade-wrap">
          <div class="ps-flex row-between">
            <div class="year-grade">
              <img src="@/assets/images/chart/custom/grade_A.png" alt="" srcset="">
              <div>上年度综合等级</div>
            </div>
            <div class="check-grade">
              <img src="@/assets/images/chart/custom/grade_B.png" alt="" srcset="">
              <div>本次检查动态食安等级</div>
            </div>
          </div>
          <div class="ps-flex row-between m-t-5">
            <div class="green-color">A级：优秀</div>
            <div class="yellow-color">B级：良好</div>
            <div class="red-color">A级：一般</div>
          </div>
        </div>
        <div class="tell-wrap ps-flex row-between col-center">
          <div>
            <div>投诉电话:</div>
            <div class="m-b-5">020-83740356</div>
            <div>投诉邮箱:</div>
            <div class="m-b-5"><EMAIL></div>
            <div>投诉通道:</div>
            <div>微信扫一扫进入投诉通道</div>
          </div>
          <div>
            <img src="@/assets/images/chart/custom/tell_code.png" alt="" srcset="">
          </div>
        </div>
      </div>
      <div class="info-item right">
        <div class="info-header ps-flex col-center" >
          <img src="@/assets/images/chart/custom/meal_info_icon.png" alt="" srcset="">
          <div class="text">食安管理机构公示</div>
        </div>
        <div class="leader-list">
          <div v-for="(item, index) in leaderList" :key="index" class="leader-item">
            <div class="img-item">
              <img :src="item.img" alt="" srcset="">
              <div class="position-item">{{item.unit}}</div>
            </div>
            <div class="name-item">{{item.name}}</div>
            <div class="phone-item">{{item.mobile}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { getSevenDateRange, divide } from '@/utils'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import  { Vue3SeamlessScroll} from 'vue3-seamless-scroll'
import { apiGetFoodReservedList } from '@/api/path'
import { StorageEnum } from '@/enums/storageEnum'
import { getLocalStorage } from '@/utils'
import { useRoute } from 'vue-router'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import huaxi1 from "@/assets/images/demo/huaxi1.png"
import huaxi2 from "@/assets/images/demo/huaxi2.png"
import huaxi3 from "@/assets/images/demo/huaxi3.png"
import huaxi4 from "@/assets/images/demo/huaxi4.png"
import huaxi5 from "@/assets/images/demo/huaxi5.png"
import huaxi6 from "@/assets/images/demo/huaxi6.png"

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);
// 配置
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option)
})

const leaderList = [
  {
    img: huaxi4,
    name: "刘海柱",
    mobile: "15801424488",
    unit: "第一责任人"
  },
  {
    img: huaxi2,
    name: "陈浩",
    mobile: "13425636522",
    unit: "辖区监管员"
  },
  {
    img: huaxi3,
    name: "李洪明",
    mobile: "15823562214",
    unit: "食品安全负责人"
  },
  {
    img: huaxi1,
    name: "于倩",
    mobile: "13756498878",
    unit: "辖区监管员"
  },
  {
    img: huaxi5,
    name: "黄盛博",
    mobile: "13644548898",
    unit: "辖区监管员"
  },
  {
    img: huaxi6,
    name: "李思恒",
    mobile: "13422521991",
    unit: "辖区监管员"
  }
]

let organizationId
const info = getLocalStorage(StorageEnum.GO_SYSTEM_STORE)
const route = useRoute()
if (info) {
  organizationId = info.userInfo.use_org
} else {
  organizationId = Number(route.query.organization_id);
}
const chartEditStore = useChartEditStore()
const selectValue = chartEditStore.getControlGlobalConfigByKey('ChooseSelect')

const totalData = reactive({
  value: Array<any>()
})

const dateRange = getSevenDateRange(7)

// 留样仪
// const getFoodReservedList = async () => {
//   const res = await apiGetFoodReservedList({
//     org_ids: [organizationId],
//     start_date: dateRange[0],
//     end_date: dateRange[1],
//   })
//   if (res && res.data) {
//     totalData.value = res.data.results.map(item => {
//       let nameList:Array<String> = []
//       item.food_detail.map(food => {
//         nameList.push(food.name)
//       })
//       item.food_name = nameList.join("、")
//       return item
//     })
//   }
// }

// 页面加载
onMounted(() => {
  console.log("onMounted");  
})

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseSelect'),
  (newData: any) => {
    const selectValue = chartEditStore.getControlGlobalConfigByKey('ChooseSelect')
    console.log('ChooseSelect11', selectValue)
  },
  {
    immediate: true,
    deep: true
  }
)


</script>

<style scoped lang="scss">
.info-announcement {
  .table-wrap{
    background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
    padding: 15px 0 10px;
    display: flex;
    justify-content: space-between;
    .info-item{
      padding: 0 15px;
      width: 100%;
    }
    .info-item.left {
      border-right: #003C60 1px solid;
    }
    .info-header{
      margin-bottom: 10px;
      img{
        width: 32px;
      }
      .text{
        color: #0BF9FE;
        font-size: 18px;
        font-style: italic;
        letter-spacing: 2px;
        font-weight: bold;
      }
    }
    .grade-wrap{
      text-align: center;
      border-bottom: #003C60 1px solid;
      padding-bottom: 12px;
      img{
        width: 84px;
      }
      .green-color{
        color: #19E285;
      }
      .yellow-color{
        color: #FEAA35;
      }
      .red-color{
        color: #FF4343;
      }
    }
    .tell-wrap{
      padding-top: 10px;
      color: #26BDFF;
      font-size: 12px;
    }
    .leader-list{
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .leader-item{
        text-align: center;
        .img-item{
          width: 76px;
          height: 98px;
          padding: 3px;
          border: 1px solid #02A9FF80;
          position: relative;
          img{
            width: 76px;
            height: 92px;
          }
          .position-item{
            position: absolute;
            bottom: 3px;
            left: 3px;
            width: 76px;
            background: #268BFFB2;
            padding: 3px;
            text-align: center;
            box-sizing: border-box;
            line-height: 16px;
          }
        }
        .name-item, .phone-item{
          font-size: 12px;
        }
        .phone-item{
          color: #FFFFFF80;
        }
      }
    }
  }
}
</style>