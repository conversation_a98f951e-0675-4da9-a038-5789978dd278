import { ConfigType, PackagesCategoryEnum, ChartFrameEnum } from '@/packages/index.d'
import { CustomCategoryEnum, CustomCategoryEnumName } from '../../index.d'

export const SampleRecord: ConfigType = {
  key: 'SampleRecord',
  chartKey: 'VSampleRecord',
  conKey: 'VCSampleRecord',
  title: '留样记录',
  category: CustomCategoryEnum.Collect,
  categoryName: CustomCategoryEnumName.Collect,
  package: PackagesCategoryEnum.CUSTOM,
  chartFrame: ChartFrameEnum.COMMON,
  image: 'sample_record.png'
}
