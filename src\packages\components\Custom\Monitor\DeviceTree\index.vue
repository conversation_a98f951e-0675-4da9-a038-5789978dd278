<template>
  <div :style="`width:${w}px;height:${h}px;`" class="device-tree m-t-10">
    <fund-head :title="option.value.titleValue" :style="`width:${w}px`">
    </fund-head>
    <div class="device-list ps-flex p-t-10" :style="`width:${w}px;height:${contentHeight}px;`" v-loading="dataLoading">
      <div v-for="(item, index) in dataList" :key="index" :class="['device-tag', 'm-l-10', 'm-t-10', currentIndexList.includes(item.id)?'active':'' ]" @click="handlderClickDevice(item,index)">
        <img :src="IcCamearBlue" alt="" class="divice-img">
        <div class="device-name m-t-10">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, watchEffect, onMounted, reactive, onUnmounted } from 'vue'
import config from './config'
import { FundHead } from '@/components/FundHead'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { DeviceTree } from './index'
import cloneDeep from 'lodash/cloneDeep'
import IcCamearBlue from '@/assets/images/chart/custom/ic_camera_blue.png'
import { apiBackgroundFundSupervisionBigShieldDeviceTreePost } from '@/api/path'
import { useRoute } from 'vue-router'
import { ProjectInfoEnum } from '@/store/modules/chartEditStore/chartEditStore.d'

const route = useRoute()

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
// 长宽
const { w, h } = toRefs(props.chartConfig.attr)
const contentHeight = ref((h.value - 42))
console.log("w", w, "h", h);
// 控件的key
const componentKey = DeviceTree.key
// 配置
const option = reactive({
  value: cloneDeep(props.chartConfig.option)
})
console.log("option", option);
// 数据列表
const dataList = ref<Array<any>>([])
// 数据加载中
const dataLoading = ref(false)
// 当前选中的索引
const currentIndexList = ref<Array<number>>([])

//  保存的数据
const chartEditStore = useChartEditStore()
// 区域改变
const handlerClickArea = (item: any) => {
  console.log("item", item);
}
// 初始化数据
const initData = async () => {
  dataList.value = [
    {
      "id": 1,
      "name": "广州空港实验中学"
    },
    {
      "id": 2,
      "name": "平沙培英学校粗加工"
    },
    {
      "id": 3,
      "name": "平沙培英学校配餐间1"
    },
    {
      "id": 4,
      "name": "平沙培英学校配餐间3"
    }
  ]
  console.log('dataList.value', dataList.value)
  //  getDataList()
}
// 获取数据
const getDataList = async (type?: any) => { 
  dataLoading.value = true
  let res = await apiBackgroundFundSupervisionBigShieldDeviceTreePost({
    channel_id :option.value.selectValue ? option.value.selectValue : Number(route.query.channel_id),
    role_id: Number(route.query.role_id)
  })
  dataLoading.value = false
  if(res && res.code === 0 ) {
    const data = res.data || {}
    const items = data.items || []
    dataList.value = cloneDeep(items)
    if(items && items.length) {
      // 设置默认选中
      items.forEach((item:any) => { 
         const name = item.name
         const id = item.id 
         if(name === '广州空港实验中学') {
          currentIndexList.value.push(id)
         }
         if(name === '平沙培英学校粗加工') {
          currentIndexList.value.push(id)
         }
         if(name === '平沙培英学校配餐间1') {
          currentIndexList.value.push(id)
         }
         if(name === '平沙培英学校配餐间3') {
          currentIndexList.value.push(id)
         } 
      })
      chartEditStore.setProjectInfo( 
        ProjectInfoEnum.DEVICE_ID, 
        currentIndexList.value
      )
    }
  }else {
    dataList.value = []
  }
}
// 设备点击
const handlderClickDevice = (item: any, index:number) => { 
  console.log('设备点击', item, index)
  let id = item.id || ''
  if(currentIndexList.value.includes(id)) {
    currentIndexList.value = currentIndexList.value.filter(item => item !== id)
  }else {
    currentIndexList.value.push(id)
  }
  chartEditStore.setProjectInfo( 
    ProjectInfoEnum.DEVICE_ID, 
    currentIndexList.value
  )
}
onMounted(() => { 
  initData()
})
onUnmounted(() => { 
  chartEditStore.setProjectInfo( 
    ProjectInfoEnum.DEVICE_ID, 
    []
  )
})


watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.value.selectValue) {
        option.value.selectValue = newData.value
        initData()
      } else if (option.value.selectValue !== newData.value) {
        option.value.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

</script>
<style scoped lang="scss">
.device-tree {
  .device-list {
    overflow-x: auto;
    height: 148px;
    border-left: 1px solid #082F50;
    border-right: 1px solid #082F50;
    border-bottom: 1px solid #082F50;
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    padding-bottom: 15px;

    &::-webkit-scrollbar {
      height: 10px;
      cursor: pointer;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(8, 47, 80, 0.5);
      border-radius: 5px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(11, 249, 254, 0.7);
      border-radius: 5px;
      border: 1px solid rgba(11, 249, 254, 0.3);
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: rgba(11, 249, 254, 0.9);
    }

    .device-tag {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      margin: 0 10px 0 0;
      padding: 5px;
      min-width: 100px;
      height: 110px;
      flex-shrink: 0;

      &:first-child {
        margin-left: 0;
      }

      .divice-img {
        width: 64px;
        height: 64px;
        margin-bottom: 5px;
      }

      .device-name {
        font-size: 14px;
        width: 84px;
        height: 46px;
        color: #fff;
        text-align: center;
        margin-top: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        word-break: break-word;
        line-height: 1.2;
      }
    }
    .active {
      border: 1px solid #26BDFF; 
    }
  }
}
</style>