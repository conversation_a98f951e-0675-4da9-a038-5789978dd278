<template>
  <div :style="`width:${w}px;height:${h}px;`" class="vistor-park c-white">
    <!--标题-->
    <ps-chart-head :title="option.value.titleValue" :style="`width:${w}px`"></ps-chart-head>
    <div>
    <!--内容-->
     <div class="">
      <vue3-seamless-scroll
       class="scroll"
       v-model='option.value.isScroll'
       :list="option.value.dataset"
       :step="0.5"
       :hover="true"
       :limit-scroll-num="3"
       :wheel="true"
       >
        <div  class='tag ps-flex flex-align-center' v-for="(item, index) in option.value.dataset" :key="index">
          <!--头像-->
          <div class="head"><img class='img w-46 h-46' :src="item.img"/></div>
          <!--人员信息-->
          <div class="m-l-10">
            <div class="font-size-16 ">{{ item.name }}</div>
            <div class="font-size-12 m-t-12">被访人：{{ item.vistor }}, {{item.relationship}}<span class="font-size-12 m-l-28">有效期：{{ item.validDate }}</span></div>
          </div>
           <!--访客权限-->
          <div class="vistor ps-flex">
              <div v-for="(subItem,subIndex) in item.typeList" :key="subIndex" class="ps-flex">
                <div class="m-l-20"><img :src="subItem.status ? option.value.checkGreenImage : option.value.checkOrangeImage" class="w-12 h-12"/></div>
                <div class="font-size-12 m-l-5 m-t-2"> {{ subItem.name }}</div>
              </div>
          </div>
         </div>
      </vue3-seamless-scroll>
     </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { isPreview } from '@/utils'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import  { Vue3SeamlessScroll} from 'vue3-seamless-scroll'
import  PsChartHead from '@/components/PsChartHead/index.vue'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);
// 配置
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option)
})
console.log("option", option);


// 页面加载
onMounted(() => {
  console.log("onMounted");  
})

// dataset 无法变更条数的补丁
watch(
  () => props.chartConfig.option,
  (newData: any) => {
    console.log("newData有新得值更新", newData);
    option.value = cloneDeep(newData)
  },
  {
    immediate: true,
    deep: true
  }
)


</script>

<style scoped lang="scss">
.vistor-park {
  color: #fff;
  overflow: hidden;
  .title {
    font-size: 22rpx;
    color: #fff;
  }
  .tag {
    min-width: 479px;
    min-height: 64px;
    background-color: #0E1D20;
    margin-top: 5px;
    padding: 0 20px;
    position: relative;
    .head {
      width: 48px;
      height: 48px;
      overflow: hidden;
      .img{
        border-radius: 24px;
        border: 1px solid #06F9A3;
      }
    }
    .vistor {
      position: absolute;
      top: 6px;
      right: 20px;
    }

  }
  .scroll {
  /* 必需要设置合适的高,因为他的原理是往列表最后添加列表重复元素，所以这个组件高不要大于其内容原先的最大高度 */
    height: 240px;
    min-width: 450px;
    overflow: hidden;
  }

}
</style>