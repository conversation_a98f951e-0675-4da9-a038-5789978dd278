<template>
  <div :style="`width:${w}px;height:${h}px;`" class="pie-income c-white">
    <!--标题-->
    <fund-head :title="option.value.titleValue" :style="`width:${w}px`">
    </fund-head>
    <div class="pie-wrap">
      <div class="pie-chart" :style="`width:${h - 75}px;height:${h - 75}px;`">
        <v-chart ref="vChartRef" :init-options="initOptions" :theme="themeColor" :option="option.value.chartOpts"
          autoresize></v-chart>
      </div>
      <div class="pie-data">
        <div class="pie-data-item" v-for="(item, index) in labelList" :key="index">
          <div :class="['item-name', item.class]">{{ item.name }}</div>
          <div class="item-price">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { isPreview } from '@/utils'
import VChart from 'vue-echarts'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import { use, graphic } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { DatasetComponent, GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { useChartDataFetch } from '@/hooks'
import FundHead from '@/components/FundHead/index.vue'
import { apiBackgroundFundSupervisionBigShieldServiceProviderData } from '@/api/path'
import { useRoute } from 'vue-router'
import { divide } from '@/utils'
const route = useRoute()

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);

use([DatasetComponent, CanvasRenderer, LineChart, GridComponent, TooltipComponent, LegendComponent])
const chartEditStore = useChartEditStore()

const option = reactive({
  value: props.chartConfig.option,
  channelId: Number(route.query.channel_id),
  roleId: Number(route.query.role_id),
  selectValue: 0
})
// 数据列表
const labelList = ref<Array<any>>(
  [
    {
      name: '食堂托管服务商',
      value: 2,
      class: 'green',
      key: 'group_meal_company_count'
    },
    {
      name: '食材配送服务商',
      value: 2,
      class: 'yellow',
      key: 'supplier_count'
    },
    {
      name: '校外配餐服务商',
      value: 0,
      class: 'red',
      key: 'catering_company_count'
    }
  ]
)
// 获取数据
const getTableData = async (type?: any) => {
  let params: any = {
    role_id: option.roleId,
    channel_id: option.channelId,
  }
  const res = await apiBackgroundFundSupervisionBigShieldServiceProviderData(params)
  if (res && res.code === 0) {
    let data = res.data ? res.data : {}
    let total = data.total ? data.total : 0
    props.chartConfig.option.chartOpts.series[0].data.forEach((item : any) => {
      item.value = data[item.data_type] || 0
    })
    labelList.value.forEach((item: any) => {
      item.value = data[item.key] || 0
    })
    props.chartConfig.option.chartOpts.title.subtext = total + '家'
    console.log("props.chartConfig.option", props.chartConfig.option);
  } else {
    props.chartConfig.option.chartOpts.series[0].data.forEach(item=>{
      item.value = 0
    })
    labelList.value.forEach((item: any) => {
      item.value = 0
    })
  }
  console.log("apiBackgroundFundSupervisionBigShieldIncome", res);
}
// 页面加载
onMounted(() => {
  props.chartConfig.option.chartOpts.series[0].data.forEach((item : any) => {
    item.value = labelList.value.find((i: any) => i.key === item.data_type)?.value || 0
  })
  props.chartConfig.option.chartOpts.title.subtext = '4家'
  // getTableData()
})

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        getTableData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        getTableData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)
const { vChartRef } = useChartDataFetch(props.chartConfig, useChartEditStore)
</script>

<style scoped lang="scss">
.pie-income {
  .pie-wrap {
    position: relative;
    background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
    display: flex;
    align-items: center;
    padding: 0 15px;

    .pie-chart {
      flex: 2;
      background: url('@/assets/images/chart/custom/pie_income_bg.png') no-repeat;
      background-position: 50% 50%;
    }

    .pie-data {
      flex: 1;
      padding-left: 20px;
      padding-top: 10px;
      padding-bottom: 10px;

      .pie-data-item {
        margin-bottom: 1px;

        .item-price {
          font-size: 16px;
          font-weight: bold;
        }

        .item-name {
          margin-left: 19px;
          position: relative;
          font-size: 14px;
        }

        .item-name::before {
          content: '';
          width: 14px;
          height: 14px;
          position: absolute;
          top: 4px;
          left: -18px;
          border-radius: 4px;
        }

        .green {
          color: #685AFF;
        }

        .green::before {
          background: #685AFF;
        }

        .yellow {
          color: #0BF9FE;
        }

        .yellow::before {
          background: #0BF9FE;
        }

        .red {
          color: #17E9AD;
        }

        .red::before {
          background: #17E9AD;
        }

        .blue {
          color: #0296D9;
        }

        .blue::before {
          background: #0296D9;
        }

        .green-light {
          color: #93EF04;
        }

        .green-light::before {
          background: #93EF04;
        }

        .purple {
          color: #4B04E4;
        }

        .purple::before {
          background: #4B04E4;
        }

        .grey {
          color: #727C79;
        }

        .grey::before {
          background: #727C79;
        }

        .grey-light {
          color: #5E87B5;
        }

        .grey-light::before {
          background: #5E87B5;
        }
      }
    }
  }


}
</style>