import { PublicConfigClass } from '@/packages/public'
import { GeneralDataTable } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
export const includes = ['legend', 'xAxis', 'yAxis', 'grid']
import { chartInitConfig } from '@/settings/designSetting'
import dataJson from './data.json'

// 表格列配置接口
export interface TableColumn {
  label: string
  key: string
  type: 'index' | 'text' | 'price' | 'date'
  flex: number
}

// 表格配置接口
export interface TableConfig {
  label: string // 表格名称
  value: string // 表格唯一标识
  data: any[] // 静态数据
  width: number // 表格总宽度
  height: number // 表格总高度
  scrollHeight: number // 滚动高度
  limiScrollNum: number, // 最小滚动条数
  dateType: string, // 默认时间类型
  dateTypeOptions: string[] // 显示的时间类型选项，为空则不显示时间筛选
  apiConfig: any // API配置
  totalConfig: any // 合计配置
  tableSetting: any[] // 表头配置
}

// 数据配置
export const option = {
  titleValue: '食堂物资数据',
  tableDataType: 'CanteenMaterial',
  isScroll: true , // 是否滚动，默认是
  dataset: [],
  tableConfig: {}
}
// 导出配置
export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = GeneralDataTable.key
  public chartConfig = cloneDeep(GeneralDataTable)
  public attr = { ...chartInitConfig, w: 673, h: 528, zIndex: -1 }
  // 图表配置项
  public option = cloneDeep(option)
}
