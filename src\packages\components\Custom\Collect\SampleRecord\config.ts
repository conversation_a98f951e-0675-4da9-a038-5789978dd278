import { PublicConfigClass } from '@/packages/public'
import { SampleRecord } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import head1Image from '@/assets/images/chart/custom/ic_head_1.png'
import head2Image from '@/assets/images/chart/custom/ic_head_2.png'
import head3Image from '@/assets/images/chart/custom/ic_head_3.png'
import checkGreenImage from '@/assets/images/chart/custom/ic_check_green.png'
import checkOrangeImage from '@/assets/images/chart/custom/ic_check_orange.png'
export const includes = ['legend', 'xAxis', 'yAxis', 'grid']
import { chartInitConfig } from '@/settings/designSetting'

// 数据配置
export const option = {
  titleValue: '留样记录',
  isScroll: true , // 是否滚动，默认是
  checkGreenImage: checkGreenImage,
  checkOrangeImage: checkOrangeImage,
  dataset: [
    {
      name: '陆华',
      vistor: '陈浩',
      relationship: '其他',
      validDate: '2024年1月20日至2024年1月20日',
      typeList: [
        {
          name: '住宿',
          status: false
        },
        {
          name: '用餐',
          status: false
        },
        {
          name: '通行',
          status: true
        }
      ],
      img: head3Image
    },
    {
      name: '曾伟',
      vistor: '曾思欣',
      relationship: '父女',
      validDate: '2024年1月20日至2024年1月20日',
      typeList: [
        {
          name: '住宿',
          status: false
        },
        {
          name: '用餐',
          status: false
        },
        {
          name: '通行',
          status: true
        }
      ],
      img: head2Image
    },
    {
      name: '张凤芝',
      vistor: '冯蕊',
      relationship: '母女',
      validDate: '2024年1月20日至2024年1月20日',
      typeList: [
        {
          name: '住宿',
          status: false
        },
        {
          name: '用餐',
          status: true
        },
        {
          name: '通行',
          status: true
        }
      ],
      img: head1Image
    },
    {
      name: '冯晓丽',
      vistor: '冯晓晴',
      relationship: '姐妹',
      validDate: '2024年1月20日至2024年1月20日',
      typeList: [
        {
          name: '住宿',
          status: false
        },
        {
          name: '用餐',
          status: false
        },
        {
          name: '通行',
          status: true
        }
      ],
      img: head2Image
    },
    {
      name: '程东',
      vistor: '冯星宇',
      relationship: '朋友',
      validDate: '2024年1月20日至2024年1月20日',
      typeList: [
        {
          name: '住宿',
          status: false
        },
        {
          name: '用餐',
          status: true
        },
        {
          name: '通行',
          status: true
        }
      ],
      img: head3Image
    },
    {
      name: '何树林',
      vistor: '何子华',
      relationship: '父子',
      validDate: '2024年1月20日至2024年1月20日',
      typeList: [
        {
          name: '住宿',
          status: false
        },
        {
          name: '用餐',
          status: false
        },
        {
          name: '通行',
          status: true
        }
      ],
      img: head3Image
    },
    {
      name: '李悦',
      vistor: '李欣欣',
      relationship: '亲戚',
      validDate: '2024年1月20日至2024年1月20日',
      typeList: [
        {
          name: '住宿',
          status: false
        },
        {
          name: '用餐',
          status: false
        },
        {
          name: '通行',
          status: true
        }
      ],
      img: head1Image
    },
    {
      name: '梁仲春',
      vistor: '梁芮莹',
      relationship: '父女',
      validDate: '2024年1月20日至2024年1月20日',
      typeList: [
        {
          name: '住宿',
          status: false
        },
        {
          name: '用餐',
          status: false
        },
        {
          name: '通行',
          status: true
        }
      ],
      img: head2Image
    }
  ]
}
// 导出配置
export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = SampleRecord.key
  public chartConfig = cloneDeep(SampleRecord)
  public attr = { ...chartInitConfig, w: 506, h: 421, zIndex: -1 }
  // 图表配置项
  public option = cloneDeep(option)
}
