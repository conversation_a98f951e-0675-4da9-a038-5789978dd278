<template>
  <div :style="`width:${w}px;height:${h}px;color: #fff`" class="scroll-merge-table">
    <!--标题-->
    <fund-head
      :title="option.value.titleValue"
      :style="`width:${w}px`">
    </fund-head>
    <div>
    <!--内容-->
      <div class="table-wrap" style="
        background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
        border: 1px solid;
        border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
        padding: 15px 20px 10px;
      ">
        <div class="table-header" style="
          position: relative;
          width: 555px; /* 设置你的div宽度 */
          height: 36px; /* 设置你的div高度 */
          overflow: hidden; /* 确保伪元素不会溢出div */
          font-size: 16px;
          border: 1px solid #02A9FF66;
          background: #04253D;
          box-sizing: border-box;
        ">
          <div class="table-header-top"></div>
          <div class="table-header-bottom"></div>
          <div class="ps-flex row-between" style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          ">
            <div class="item-title flex-1" style="
              display: flex;
              flex: 1;
              height: 36px;
              line-height: 36px;
              color: #26BDFF;
              text-align: center;
            ">星期</div>
            <div class="item-title flex-1" style="
              display: flex;
              flex: 1;
              height: 36px;
              line-height: 36px;
              color: #26BDFF;
              text-align: center;
            ">餐次</div>
            <div class="item-title flex-1" style="
              display: flex;
              flex: 1;
              height: 36px;
              line-height: 36px;
              color: #26BDFF;
              text-align: center;
            ">食物名称</div>
            <div class="item-title flex-1" style="
              display: flex;
              flex: 1;
              height: 36px;
              line-height: 36px;
              color: #26BDFF;
              text-align: center;
            ">菜品重量</div>
            <div class="item-title flex-1" style="
              display: flex;
              flex: 1;
              height: 36px;
              line-height: 36px;
              color: #26BDFF;
              text-align: center;
            ">菜品价格</div>
            <div class="item-title flex-1" style="
              display: flex;
              flex: 1;
              height: 36px;
              line-height: 36px;
              color: #26BDFF;
              text-align: center;
            ">配料</div>
            <div class="item-title flex-1" style="
              display: flex;
              flex: 1;
              height: 36px;
              line-height: 36px;
              color: #26BDFF;
              text-align: center;
            ">食材重量</div>
          </div>
        </div>
        <div style="height: 305px;overflow: hidden;">
          <vue3-seamless-scroll
            class="scroll"
            v-model="isScroll"
            :list="totalData"
            :step="0.5"
            :hover="true"
            :limit-scroll-num="3"
            :wheel="true"
            >
              <div class='tag-item' v-for="(item, index) in totalData" :key="index" style="
                min-width: 479px;
                min-height: 40px;
                color: #FFF;
                text-align: center;
                font-size: 16px;
              ">
                <div class="flex-box-wrap" style="
                  display: flex;
                  height: 100%;
                ">
                  <div class="parent-item flex-1" style="
                    flex: 1;
                    position: relative;
                    border-right: 1px solid #02A9FF66;
                    border-bottom: 1px solid #02A9FF66;
                  ">
                    <div class="parent-item-text" style="
                      width: 100%;
                      position: absolute;
                      top: 50%;
                      transform: translate(0, -50%);
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    ">{{item.date}}</div>
                  </div>
                  <div class="parent-item flex-1" style="
                    flex: 1;
                    position: relative;
                    border-right: 1px solid #02A9FF66;
                    border-bottom: 1px solid #02A9FF66;
                  ">
                    <div class="parent-item-text" style="
                      width: 100%;
                      position: absolute;
                      top: 50%;
                      transform: translate(0, -50%);
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    ">{{item.meal_period_type}}</div>
                  </div>
                  <div class="parent-item flex-1" style="
                    flex: 1;
                    position: relative;
                    border-right: 1px solid #02A9FF66;
                    border-bottom: 1px solid #02A9FF66;
                  ">
                    <div class="parent-item-text" style="
                      width: 100%;
                      position: absolute;
                      top: 50%;
                      transform: translate(0, -50%);
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    ">{{item.food_name}}</div>
                  </div>
                  <div class="parent-item flex-4" style="
                    flex: 4;
                    position: relative;
                    border-right: 1px solid #02A9FF66;
                    border-bottom: 1px solid #02A9FF66;
                  ">
                    <div v-for="(child, childIndex) in item.childList" :key="childIndex" class="ps-flex flex-align-center" style="
                      display: flex;
                      align-items: center;
                      justify-content: center;
                    ">
                      <div class="child-item flex-1" style="
                        flex: 1;
                        line-height: 40px;
                        height: 40px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        border-right: 1px solid #02A9FF66;
                        border-bottom: 1px solid #02A9FF66;
                      ">{{child.food_weight}}</div>
                      <div class="child-item flex-1" style="
                        flex: 1;
                        line-height: 40px;
                        height: 40px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        border-right: 1px solid #02A9FF66;
                        border-bottom: 1px solid #02A9FF66;
                      ">{{child.food_price}}</div>
                      <div class="child-item flex-1" style="
                        flex: 1;
                        line-height: 40px;
                        height: 40px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        border-right: 1px solid #02A9FF66;
                        border-bottom: 1px solid #02A9FF66;
                      ">{{child.ingredients}}</div>
                      <div class="child-item flex-1" style="
                        flex: 1;
                        line-height: 40px;
                        height: 40px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        border-right: 1px solid #02A9FF66;
                        border-bottom: 1px solid #02A9FF66;
                      ">{{child.ingredients_weight}}</div>
                    </div>
                  </div>
                </div>
              </div>
          </vue3-seamless-scroll>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { getSevenDateRange, divide } from '@/utils'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import  { Vue3SeamlessScroll} from 'vue3-seamless-scroll'
import  FundHead from '@/components/FundHead/index.vue'
import { apiBackgroundFundSupervisionBigShieldRationRecipeList } from '@/api/path'
import { StorageEnum } from '@/enums/storageEnum'
// import dataJson from './data.json'
import { getLocalStorage } from '@/utils'
import { useRoute } from 'vue-router'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'

const isScroll = ref(true)
const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);
// 配置
const route = useRoute()
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option),
  totalPrice: 0.00,
  dateType: 'month',
  channelId: Number(route.query.channel_id),
  orgId: Number(route.query.org_id),
  selectValue: 0
})


const chartEditStore = useChartEditStore()
const selectValue = chartEditStore.getControlGlobalConfigByKey('ChooseTree')
console.log('ChooseSelect11', selectValue)

const totalData = ref<any>([])
// totalData.value = cloneDeep(dataJson)


const dateRange = getSevenDateRange(7)

// 留样仪
import dayjs from 'dayjs'
const WEEK_LIST = [
  { label: "星期一", value: 1 },
  { label: "星期二", value: 2 },
  { label: "星期三", value: 3 },
  { label: "星期四", value: 4 },
  { label: "星期五", value: 5 },
  { label: "星期六", value: 6 },
  { label: "星期日", value: 0 }
]
const MEAL_TYPES = [
  { label: "早餐", value: "breakfast" },
  { label: "午餐", value: "lunch" },
  { label: "下午茶", value: "afternoon" },
  { label: "晚餐", value: "dinner" },
  { label: "宵夜", value: "supper" },
  { label: "凌晨餐", value: "morning" }
]

// 核心处理逻辑
const processList = (list) => {
  // 1. 使用 reduce 进行分组
  const grouped = list.reduce((acc, item) => {
    // 2. 创建唯一分组键（完整日期 + 餐次 + 食物名）
    const key = `${item.date}_${item.meal_period_type}_${item.food_name}`

    // 3. 初始化分组容器
    if (!acc[key]) {
      acc[key] = {
        date: item.date, // 保留完整日期字符串
        meal_period_type: item.meal_period_type,
        food_name: item.food_name,
        childList: []
      }
    }

    // 4. 填充 childList
    acc[key].childList.push({
      food_weight: item.food_weight,
      food_price: item.food_price,
      ingredients: item.ingredients,
      ingredients_weight: item.ingredients_weight
    })

    return acc
  }, {})

  // 5. 转换为数组格式
  return Object.values(grouped)
}
const getFoodReservedList = async () => {
  const res = await apiBackgroundFundSupervisionBigShieldRationRecipeList({
    org_id: option.selectValue ? option.selectValue : option.orgId,
  })
  if (res && res.data) {
    let foodData = res.data
    let list: any = []
    for (const key in foodData) {
      for (const mealKey in foodData[key]) {
        let info = {
          date: "" as string,
          meal_period_type: "" as string,
          food_name: "" as string,
          food_weight: "" as string,
          food_price: "" as string,
          ingredients: "" as string,
          ingredients_weight: "" as string
        }
        let week = WEEK_LIST.filter((item) => item.value === dayjs(key).day())[0].label
        info.date = `${dayjs(key).format("YYYY年MM月DD日")} ${week}`
        info.meal_period_type = MEAL_TYPES.filter((item) => item.value === mealKey)[0].label
        if (foodData[key][mealKey].length) {
          foodData[key][mealKey].forEach((item: any) => {
            info.food_name = item.food_name
            info.food_weight = `${item.weight}g`
            info.food_price = `${divide(item.food_price)}元`
            if (item.ingredient_info_list.length) {
              // 找食材的数据，依次
              item.ingredient_info_list.forEach((itemIn: any) => {
                let newObj = cloneDeep(info)
                newObj.ingredients = itemIn.ingredient_name
                newObj.ingredients_weight = `${itemIn.ingredient_weight}g`
                list.push(newObj)
              })
            } else {
              info.ingredients = "--"
              info.ingredients_weight = "--"
              list.push(info)
            }
          })
        }
      }
    }
    console.log('list', list)
    totalData.value = processList(list)
    console.log("totalData", totalData.value)
  }
}

const formatPrice = (num: number) => {
  let aaa = divide(num)
  if (aaa >= 1000000) {
    console.log(Math.round(aaa/10000) + 'w') // floor
    return Math.round(aaa/10000) + 'w'
  } else {
    return aaa
  }
}

// 页面加载
onMounted(() => {
  console.log("onMounted");  
  getFoodReservedList()
})

// dataset 无法变更条数的补丁
watch(
  () => props.chartConfig.option,
  (newData: any) => {
    console.log("newData有新得值更新", newData);
    option.value = cloneDeep(newData)
    getFoodReservedList()
  },
  {
    immediate: true,
    deep: true
  }
)

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    const selectValue = chartEditStore.getControlGlobalConfigByKey('ChooseTree')
    if (selectValue?.value === null) {
      option.selectValue = 0
    } else {
      option.selectValue = selectValue?.value
    }
    getFoodReservedList()
  },
  {
    immediate: true,
    deep: true
  }
)


</script>

<style scoped lang="scss">
.merge-table {
  .table-wrap{
    // background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
    // border: 1px solid;
    // border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
    // padding: 15px 20px 10px;
  }
  .table-header{
    // position: relative;
    // width: 555px; /* 设置你的div宽度 */
    // height: 36px; /* 设置你的div高度 */
    // overflow: hidden; /* 确保伪元素不会溢出div */
    // font-size: 16px;
    // border: 1px solid #02A9FF66;
    // background: #04253D;
    // box-sizing: border-box;
    // .item-title{
    //   height: 36px;
    //   line-height: 36px;
    //   color: #26BDFF;
    //   text-align: center;
    // }

    .table-header-top::before,
    .table-header-top::after,
    .table-header-bottom::before,
    .table-header-bottom::after {
      content: '';
      position: absolute;
      width: 6px; /* 短边框的宽度 */
      height: 6px; /* 短边框的高度 */
    }
    
    .table-header-top::before {
      top: 0;
      right: 0;
      border-top: 2px solid #06A5FF;
      border-right: 2px solid #06A5FF;
    }
    
    .table-header-top::after {
      top: 0;
      left: 0;
      border-top: 2px solid #06A5FF;
      border-left: 2px solid #06A5FF;
    }
    .table-header-bottom::before {
      bottom: 0;
      right: 0;
      border-bottom: 2px solid #06A5FF;
      border-right: 2px solid #06A5FF;
    }
    
    .table-header-bottom::after {
      bottom: 0;
      left: 0;
      border-bottom: 2px solid #06A5FF;
      border-left: 2px solid #06A5FF;
    }
  }
  
  .tag-item {
    // min-width: 479px;
    // min-height: 40px;
    // color: #FFF;
    // text-align: center;
    // font-size: 16px;
    // .parent-item{
    //   position: relative;
    //   border-right: 1px solid #02A9FF66;
    //   border-bottom: 1px solid #02A9FF66;
    // }
    .parent-item:first-child{
      border-left: 1px solid #02A9FF66;
    }
    // .parent-item-text{
    //   width: 100%;
    //   position: absolute;
    //   top: 50%;
    //   transform: translate(0, -50%);
    //   overflow: hidden;
    //   text-overflow: ellipsis;
    //   white-space: nowrap;
    // }
    // .child-item{
    //   line-height: 40px;
    //   height: 40px;
    //   overflow: hidden;
    //   text-overflow: ellipsis;
    //   white-space: nowrap;
    //   border-right: 1px solid #02A9FF66;
    //   border-bottom: 1px solid #02A9FF66;
    // }
  }
  .tag-item:nth-child(2n-1) {
    background: #0E48724D;
  }
  .flex-box-wrap{
    // display: flex;
    // height: 100%;
  }

  .flex-5{
    flex: 5;
  }
  .flex-4{
    flex: 4;
  }
  .flex-3{
    flex: 3;
  }
  .flex-2{
    flex: 2;
  }
  .flex-1{
    flex: 1;
  }
  .green-color{
    color: #0BF9FE!important;
  }
  .red-color{
    color: #FF4343!important;
  }
  .blue-color{
    color: #26BDFF!important;
  }
  .f-s-28{
    font-size: 28px;
  }
  .f-w-600{
    font-weight: 600;
  }

  .scroll {
  /* 必需要设置合适的高,因为他的原理是往列表最后添加列表重复元素，所以这个组件高不要大于其内容原先的最大高度 */
    height: 305px;
    min-width: 450px;
    overflow: hidden;
  }
  .scroll-230 {
  /* 必需要设置合适的高,因为他的原理是往列表最后添加列表重复元素，所以这个组件高不要大于其内容原先的最大高度 */
    height: 230px;
    min-width: 450px;
    overflow: hidden;
  }

}
</style>