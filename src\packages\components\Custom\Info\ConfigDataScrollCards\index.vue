<template>
  <div :style="`width:${w}px;height:${h}px;`" class="config-data-scroll-cards c-white">
    <!--标题-->
    <table-head
      v-if="currentCardConfig.showTableHead"
      :title="option.value.titleValue"
      :dateTypeOptions="currentDateTypeOptions"
      :date-type="option.dateType"
      :style="`width:${w}px`"
      @select-handle="selectHandle">
    </table-head>
    
    <div class="scroll-wrap"
      :style="`height:${currentCardConfig.scrollHeight ? currentCardConfig.scrollHeight : 305}px;overflow: hidden;`">
      <vue3-seamless-scroll class="scroll" v-model='option.value.isScroll' :list="totalData.value" :step="0.5"
        :hover="true" :limit-scroll-num="currentCardConfig.limiScrollNum" :wheel="true">
        <div class='tag-item' :class="currentDisplayMode" v-for="(item, index) in totalData.value" :key="index">
          <!-- 不同模式 -->
          <div v-if="currentDisplayMode === 'accompany_meal'" :class="shouldShowDivider(index)" class="mode-item">
            <div class="meal-type ps-flex col-center">
              <div class="meal-dot" :style="getColorStyle(index, 'colorList', 'backgroundColor')"></div>
              <div>{{ item.meal_type }}</div>
            </div>
            <div class="name-info">{{ item.name_info }}</div>
          </div>
        </div>
      </vue3-seamless-scroll>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { isPreview } from '@/utils'
import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import { TableHead } from '@/components/TableHead'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { useRoute } from 'vue-router'
import { MEAL_TYPES } from '@/utils/constants'
import { accompany_meal } from './data'
import {
  apiBackgroundFundSupervisionBigShieldMealAccompanyData
} from '@/api/path'

// API 映射
const apiMap: Record<string, any> = {
  apiBackgroundFundSupervisionBigShieldMealAccompanyData
}


const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})

const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)
const route = useRoute()
const chartEditStore = useChartEditStore()

// 长宽获取
const { w, h } = toRefs(props.chartConfig.attr)

// 配置
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option),
  channelId: Number(route.query.channel_id),
  orgId: Number(route.query.org_id),
  selectValue: 0,
  dateType: ''
})

const totalData = reactive({
  value: Array<any>()
})

// 获取默认卡片配置
const getDefaultCardConfig = () => {
  return {
    label: '可滚动信息卡片',
    value: "custom",
    data: [],
    width: 288,
    height: 178,
    scrollHeight: 128,
    limiScrollNum: 3,
    displayMode: '',
    showTableHead: true,
    showDivider: true,
    dateType: '',
    dateTypeOptions: [],
    apiConfig: {
      apiFunction: '',
      apiParams: ['']
    }
  }
}

// 当前卡片配置
const currentCardConfig = computed(() => {
  const config = option.value.cardConfig || getDefaultCardConfig()
  // 确保所有必要属性都有默认值
  return {
    ...getDefaultCardConfig(),
    ...config
  }
})

// 当前API 配置
const apiConfig = computed(() => {
  const config = currentCardConfig.value as any
  return config.apiConfig || {
    apiFunction: '',
    apiParams: []
  }
})

// 显示数据
const displayData = computed(() => {
  const data = (option.value.dataset as any) || {}
  // 确保所有必要的属性都有默认值
  return {
    total: data.total || 0,
    normal: data.normal || 0,
    abnormal: data.abnormal || 0,
    ...data
  }
})

// 当前显示模式
const currentDisplayMode = computed(() => {
  const config = currentCardConfig.value as any
  return config.displayMode || 'amount'
})

// 当前时间选项
const currentDateTypeOptions = computed(() => {
  const config = currentCardConfig.value as any
  return config.dateTypeOptions || ['week']
})

const getColorStyle = (index: number, key: string, type: string = 'backgroundColor') => {
  const config = currentCardConfig.value as any
  const colors = config[key] || ['#0BF9FE', '#FFC73A', '#0BF9FE']
  return {
    [type]: colors[index] || colors[0],
  }
}

const shouldShowDivider = (index: number) => {
  const config = currentCardConfig.value as any
  return (config.showDivider !== false && index < totalData.value.length - 1) ? 'divider' : ''
}

// 获取数据
const getTableData = async (type?: any) => {
  let params: any = {}
  if (apiConfig.value.apiParams) {
    apiConfig.value.apiParams.forEach((paramKey: string) => {
      // 配置apiParams的时候，org_id、org_list、channel_id 三选一，指代的是左上角选择框，请注意。
      switch (paramKey) {
        case 'org_id':
          params.org_id = option.selectValue ? option.selectValue : option.orgId
          break
        case 'org_list':
          params.org_list = option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined
          break
        case 'channel_id':
          params.channel_id = option.selectValue ? option.selectValue : option.channelId
          break
        case 'date_type':
          params.date_type = type ? type : option.dateType
          break
        default:
          params[paramKey] = null
      }
    })
  }
  const res = await apiMap[apiConfig.value.apiFunction](params)
  if (res.code === 0) {
    if (res && res.data) {
      if (option.value.cardDataType === 'accompany_meal') {
        totalData.value = []
        for (let key in res.data) {
          totalData.value.push({
            meal_type: key,
            name_info: res.data[key].join('、')
          })
        }
        // 根据餐段MEAL_TYPES的key的顺序排序
        totalData.value.sort((a, b) => {
          return MEAL_TYPES.findIndex(item => item.label === a.meal_type) - MEAL_TYPES.findIndex(item => item.label === b.meal_type)
        })
      }
    }
  }
}

// 获取筛选后的数据
const getFilteredData = () => {
  if (option.value.cardDataType === 'accompany_meal') {
    const allData = accompany_meal
    let dataKey
    if (option.selectValue) {
      dataKey = option.selectValue
    } else {
      // 如果没有指定 selectValue，使用第一个可用的键
      dataKey = Object.keys(allData)[0] || 'shangyouerzhong'
    }

    const result = allData[dataKey] || []
    return result
  }

  return []
}

// 事件处理
const selectHandle = (type: string) => {
  option.dateType = type
  // 这里可以添加数据获取逻辑
  if (option.value.cardDataType === 'accompany_meal') {
    // 使用本地数据筛选
    const filteredData = getFilteredData()
    totalData.value = filteredData
  } else {
    // 使用 API 获取数据
    if (option.selectValue) {
      getTableData(option.dateType)
    }
  }
}

// 初始化
const initData = () => {
  option.dateType = currentCardConfig.value.dateType
  if (option.value.cardDataType === 'accompany_meal') {
    // 使用本地数据筛选
    const filteredData = getFilteredData()
    totalData.value = filteredData
  } else {
    if (option.selectValue) {
      getTableData(option.dateType)
    } else {
      // totalData.value = displayData.value
    }
  }
}

// 页面加载
onMounted(() => {
  initData()
})

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseSelect'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)
</script>

<style scoped lang="scss">
.config-data-scroll-cards {
  background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
  border: 1px solid;
  border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7)) 1;
  box-sizing: border-box;

  .content-data {
    padding: 5px;
    gap: 20px;
  }

  .mode-item{
    display: flex;
    align-items: center;
    padding: 10px 0;
    gap: 10px;
  }

  .accompany_meal{
    padding:  0px 10px;
    .meal-type{
      width: 100px;
      .meal-dot{
        width: 10px;
        height: 10px;
        border-radius: 2px;
        margin-right: 10px;
      }
    }
    .name-info{
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: right;
    }
  }

  .divider {
    border-bottom:1px dashed #003C60;
  }

}
</style>
