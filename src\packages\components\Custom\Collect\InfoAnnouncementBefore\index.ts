import { ConfigType, PackagesCategoryEnum, ChartFrameEnum } from '@/packages/index.d'
import { CustomCategoryEnum, CustomCategoryEnumName } from '../../index.d'

export const InfoAnnouncementBefore: ConfigType = {
  key: 'InfoAnnouncementBefore',
  chartKey: 'VInfoAnnouncementBefore',
  conKey: 'VCInfoAnnouncementBefore',
  title: '食安信息公示',
  category: CustomCategoryEnum.Collect,
  categoryName: CustomCategoryEnumName.Collect,
  package: PackagesCategoryEnum.CUSTOM,
  chartFrame: ChartFrameEnum.COMMON,
  image: 'info_announcement.png'
}
