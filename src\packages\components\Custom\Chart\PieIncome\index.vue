<template>
  <div :style="`width:${w}px;height:${h}px;`" class="pie-income c-white">
    <!--标题-->
    <fund-head :title="option.value.titleValue" :style="`width:${w}px`">
    </fund-head>
    <div class="pie-wrap">
      <div class="pie-chart" :style="`width:${h - 75}px;height:${h - 75}px;`">
        <v-chart ref="vChartRef" :init-options="initOptions" :theme="themeColor" :option="option.value.chartOpts"
          autoresize></v-chart>
      </div>
      <div class="pie-data">
        <div class="pie-data-item" v-for="(item, index) in labelList" :key="index">
          <div :class="['item-name', item.class]">{{ item.name }}</div>
          <div class="item-price">￥{{ item.value }}</div>
        </div>
        <!-- <div class="pie-data-item">
          <div class="item-name green">预充值收入</div>
          <div class="item-price">￥{{ rechargePrice }}</div>
        </div>
        <div class="pie-data-item">
          <div class="item-name yellow">第三方消费收入</div>
          <div class="item-price">￥{{ consumePrice }}</div>
        </div>
        <div class="pie-data-item">
          <div class="item-name red">伙食缴费收入</div>
          <div class="item-price">￥{{ jiaoFeiPrice }}</div>
        </div>
        <div class="pie-data-item">
          <div class="item-name blue">财政补贴</div>
          <div class="item-price">￥{{ governmentSubsidy }}</div>
        </div>
        <div class="pie-data-item">
          <div class="item-name green-light">公益捐赠</div>
          <div class="item-price">￥{{ publicWelfareDonation }}</div>
        </div>
        <div class="pie-data-item">
          <div class="item-name purple">采购退款</div>
          <div class="item-price">￥{{ 0 }}</div>
        </div>
        <div class="pie-data-item">
          <div class="item-name grey">非营业性-其他</div>
          <div class="item-price">￥{{ nonOperatingOtherIncome }}</div>
        </div>
        <div class="pie-data-item">
          <div class="item-name grey-light">营业性-其他</div>
          <div class="item-price">￥{{ operatingOtherIncome }}</div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { isPreview } from '@/utils'
import VChart from 'vue-echarts'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import { use, graphic } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { DatasetComponent, GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { useChartDataFetch } from '@/hooks'
import FundHead from '@/components/FundHead/index.vue'
import { apiBackgroundFundSupervisionBigShieldIncomeDistribution, apiBackgroundFundSupervisionBigShieldDietIncomeDistribution } from '@/api/path'
import { useRoute } from 'vue-router'
import { divide } from '@/utils'
import { FINANCE_LABEL_LIST, FINANCE_OPTION_DATA, DIET_LABEL_LIST, DIET_OPTION_DATA, mealExpensesScreen } from './data'
const route = useRoute()

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);

use([DatasetComponent, CanvasRenderer, LineChart, GridComponent, TooltipComponent, LegendComponent])
const chartEditStore = useChartEditStore()

const option = reactive({
  value: props.chartConfig.option,
  dateType: 'month',
  channelId: Number(route.query.channel_id),
  orgId: Number(route.query.org_id),
  selectValue: 0
})
// 第三方消费收入
const consumePrice = ref(0)
// 财政补贴
const governmentSubsidy = ref(0)
// 缴费收入
const jiaoFeiPrice = ref(0)
// 非营业-其它
const nonOperatingOtherIncome = ref(0)
// 公益捐款
const publicWelfareDonation = ref(0)
// 营业-其它
const operatingOtherIncome = ref(0)
// 充值收入
const rechargePrice = ref(0)
// 数据列表
const labelList = ref<Array<any>>(cloneDeep(FINANCE_LABEL_LIST))
// 根据图表基本配置选择类型获取对应数据列表和chartOptions
function getLabelListBytableDataType(tableDataType:string){
  switch (tableDataType) {
    case 'financialDashboard':
      props.chartConfig.option.chartOpts.title.subtext = '合计收入'
      props.chartConfig.option.chartOpts.series[0].data = cloneDeep(FINANCE_OPTION_DATA)
      return cloneDeep(FINANCE_LABEL_LIST)
    case 'mealExpensesScreen':
      props.chartConfig.option.chartOpts.title.subtext = '合计营收'
      props.chartConfig.option.chartOpts.series[0].data = cloneDeep(DIET_OPTION_DATA)
      return cloneDeep(DIET_LABEL_LIST)
    default:
      return []
  }
}
// 获取数据
const getTableData = async (type?: any) => {
  console.log('tableDataType的值', option.value.tableDataType);
  if (!option.selectValue) {
    return
  }
  let api: any
  let params: any = {}
  switch (option.value.tableDataType) {
    case 'financialDashboard':
      params = {
        org_list: option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined,
        channel_id: option.channelId,
      }
      api = apiBackgroundFundSupervisionBigShieldIncomeDistribution
    break
    case 'mealExpensesScreen':
      params = {
        org_list: option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined,
        channel_id: option.channelId,
      }
      api = apiBackgroundFundSupervisionBigShieldDietIncomeDistribution
    break
  }
  const res = await api(params)
  if (res && res.code === 0) {
    let data = res.data ? res.data : {}
    let total = data.total ? divide(data.total) : 0
    let resultList = data.data_list || []
    switch (option.value.tableDataType) {
      // 财务大屏
      case 'financialDashboard':
        if (resultList && resultList.length > 0) {
          labelList.value = labelList.value.map((item: any) => {
            let key = item.key || ''
            let findItem = resultList.find((item: any) => item.key === key)
            if (findItem) {
              item.value = divide(findItem.price)
            } else {
              item.value = 0
            }
            return item
          })
          props.chartConfig.option.chartOpts.series[0].data.forEach((seriesItem: any) => {
            let thisType = seriesItem.data_type
            let findItem = resultList.find((item: any) => item.key === thisType)
            if (findItem) {
              seriesItem.value = divide(findItem.price) || 0
            } else {
              seriesItem.value = 0
            }
          })
        } else {
          props.chartConfig.option.chartOpts.series[0].data.forEach(item=>{
            item.value = 0
          })
          labelList.value.forEach((item: any) => {
            item.value = 0
          })
        }
        props.chartConfig.option.chartOpts.title.text = "¥" + total
      break

       // 膳食大屏
      case 'mealExpensesScreen':
        labelList.value = labelList.value.map(item => {
          return {
            ...item,
            value: divide(data[item.key]) ?? 0
          }
        })
        props.chartConfig.option.chartOpts.series[0].data.forEach((seriesItem: any) => {
          let thisType = seriesItem.data_type
          seriesItem.value = divide(data[thisType]) ?? 0
        })
        props.chartConfig.option.chartOpts.title.text = "¥" + divide(Object.values(data).reduce((acc:number, val:any) => acc + val, 0))
      break
    }
  } else {
    switch (option.value.tableDataType) {
      // 财务大屏
      case 'financialDashboard':
      case 'mealExpensesScreen':
        props.chartConfig.option.chartOpts.series[0].data.forEach(item=>{
          item.value = 0
        })
        labelList.value.forEach((item: any) => {
          item.value = 0
        })
        props.chartConfig.option.chartOpts.title.text = "¥" + 0.00
      break;
    }
  }
}

// 获取筛选后的数据
const getFilteredData = () => {
  if (option.value.tableDataType === 'mealExpensesScreen') {
    const allData = mealExpensesScreen
    let dataKey
    if (option.selectValue) {
      dataKey = option.selectValue
    } else {
      // 如果没有指定 selectValue，使用第一个可用的键
      dataKey = 'shangyouerzhong'
    }
    labelList.value = allData.data[dataKey]
    props.chartConfig.option.chartOpts.title.subtext = '合计营收'
    props.chartConfig.option.chartOpts.series[0].data = allData.serise[dataKey]
    props.chartConfig.option.chartOpts.title.text = "¥" + allData.total[dataKey]

  }

  return []
}

const initData = () => {
  if (option.value.tableDataType === 'mealExpensesScreen') {
    // 使用本地数据筛选
    getFilteredData()
  } else {
    getTableData()
  }
}

// 页面加载
onMounted(() => {
  initData()
})

// watch(
//   () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
//   (newData: any) => {
//     // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
//     if (newData) {
//       // 如果有值，并且selecctValue为空
//       if (!option.selectValue) {
//         option.selectValue = newData.value
//         initData()
//       } else if (option.selectValue !== newData.value) {
//         option.selectValue = newData.value
//         initData()
//       }
//     }
//   },
//   {
//     immediate: false,
//     deep: true
//   }
// )

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseSelect'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

watch(
  () => option.value.tableDataType,
  (newData: any) => {
    if (newData) {
      labelList.value = getLabelListBytableDataType(newData)
    }
  },
  {
    immediate: true,
    deep: true
  }
)
const { vChartRef } = useChartDataFetch(props.chartConfig, useChartEditStore)
</script>

<style scoped lang="scss">
.pie-income {
  .pie-wrap {
    position: relative;
    background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
    display: flex;
    padding: 0 15px;

    .pie-chart {
      flex: 2;
      background: url('@/assets/images/chart/custom/pie_income_bg.png') no-repeat;
      background-position: 50% 50%;
    }

    .pie-data {
      flex: 1;
      padding-left: 20px;
      padding-top: 10px;
      padding-bottom: 10px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      

      .pie-data-item {
        margin-bottom: 1px;

        .item-price {
          font-size: 16px;
          font-weight: bold;
        }

        .item-name {
          margin-left: 19px;
          position: relative;
          font-size: 14px;
        }

        .item-name::before {
          content: '';
          width: 14px;
          height: 14px;
          position: absolute;
          top: 4px;
          left: -18px;
          border-radius: 4px;
        }

        .green {
          color: #17E9AD;
        }

        .green::before {
          background: #17E9AD;
        }

        .yellow {
          color: #FFD364;
        }

        .yellow::before {
          background: #FFD364;
        }

        .red {
          color: #EF7647;
        }

        .red::before {
          background: #EF7647;
        }

        .blue {
          color: #0296D9;
        }

        .blue::before {
          background: #0296D9;
        }

        .green-light {
          color: #93EF04;
        }

        .green-light::before {
          background: #93EF04;
        }

        .purple {
          color: #4B04E4;
        }

        .purple::before {
          background: #4B04E4;
        }

        .grey {
          color: #727C79;
        }

        .grey::before {
          background: #727C79;
        }

        .grey-light {
          color: #5E87B5;
        }

        .grey-light::before {
          background: #5E87B5;
        }

        .darkblue{
          color: #23588D;
        }

        .darkblue::before {
          background-color: #23588D;
        }

        .brightcyan{
          color: #0ADDE8
        }

        .brightcyan::before{
          background-color: #0ADDE8
        }

        .purple{
          color: #6456FF;
        }

        .purple::before{
          background-color: #6456FF;
        }

        .skyBlue{
          color: #38B2FF
        }

        .skyBlue::before  {
          background-color: #38B2FF
        }
      }
    }
  }


}
</style>