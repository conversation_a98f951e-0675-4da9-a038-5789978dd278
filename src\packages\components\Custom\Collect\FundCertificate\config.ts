import { PublicConfigClass } from '@/packages/public'
import { FundCertificate } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import head1Image from '@/assets/images/chart/custom/ic_head_1.png'
import head2Image from '@/assets/images/chart/custom/ic_head_2.png'
import head3Image from '@/assets/images/chart/custom/ic_head_3.png'
import checkGreenImage from '@/assets/images/chart/custom/ic_check_green.png'
import checkOrangeImage from '@/assets/images/chart/custom/ic_check_orange.png'
export const includes = ['legend', 'xAxis', 'yAxis', 'grid']
import { chartInitConfig } from '@/settings/designSetting'
import dataJson from './data.json'

// 数据配置
export const option = {
  titleValue: '资质证书公示',
  isScroll: true , // 是否滚动，默认是
  scrollHeight: 400,
  checkGreenImage: checkGreenImage,
  checkOrangeImage: checkOrangeImage,
  dataset: dataJson.certificateList,
  tableDataType: 'certificate',
}
// 导出配置
export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = FundCertificate.key
  public chartConfig = cloneDeep(FundCertificate)
  public attr = { ...chartInitConfig, w: 596, h: 512, zIndex: -1 }
  // 图表配置项
  public option = cloneDeep(option)
}
