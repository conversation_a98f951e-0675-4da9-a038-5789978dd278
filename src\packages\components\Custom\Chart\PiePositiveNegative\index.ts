import { ConfigType, PackagesCategoryEnum, ChartFrameEnum } from '@/packages/index.d'
import { CustomCategoryEnum, CustomCategoryEnumName } from '../../index.d'

export const PiePositiveNegative: ConfigType = {
  key: 'PiePositiveNegative',
  chartKey: 'VPiePositiveNegative',
  conKey: 'VCPiePositiveNegative',
  title: '正负值柱状图',
  category: CustomCategoryEnum.Chart,
  categoryName: CustomCategoryEnumName.Chart,
  package: PackagesCategoryEnum.CUSTOM,
  chartFrame: ChartFrameEnum.COMMON,
  image: 'bar_positive_negative.png'
}
