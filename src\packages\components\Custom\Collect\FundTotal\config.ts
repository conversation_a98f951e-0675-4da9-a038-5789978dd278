import { PublicConfigClass } from '@/packages/public'
import { FundTotal } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
export const includes = ['legend', 'xAxis', 'yAxis', 'grid']
import { chartInitConfig } from '@/settings/designSetting'
import dataJson from './data.json'
// 数据配置
export const option = {
  titleValue: '资金监管总数据',
  tableDataType: 'fundData',
  dataset: [{
    title: "本月营收",
    fee: "172850",
    basis: "-8.96%",
    type: "down",
  }, {
    title: "本月支出",
    fee: "160770.5",
    basis: "-8.79%",
    type: "down",
  }, {
    title: "本月利润",
    fee: "12029.5",
    basis: "-10.94%",
    type: "down",
  }]
}
// 导出配置
export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = FundTotal.key
  public chartConfig = cloneDeep(FundTotal)
  public attr = { ...chartInitConfig, w: 596, h: 158, zIndex: -1 }
  // 图表配置项
  public option = cloneDeep(option)
}
