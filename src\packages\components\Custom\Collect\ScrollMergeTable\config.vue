<template>
  <div class="">
    <collapse-item name="基础配置" :expanded="true">
      <setting-item-box name="标题" :alone="false" :leftStyle="leftStyle">
        <n-input v-model:value ="optionData.titleValue" type="text" placeholder="请输入标题" class="input-tag" show-count :maxlength="12"></n-input>
      </setting-item-box>     
    </collapse-item>
    <collapse-item name="静态数据配置" :expanded="true">
      <setting-item-box :name="item.name" :alone="false" v-for="(item,index) in optionData.dataList" :key="index" :leftStyle="leftStyle">
         <n-input-number v-model:value ="item.count"  placeholder="请输入" clearable min="0" ></n-input-number>
      </setting-item-box>  
    </collapse-item>

  </div> 
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue'
import { GlobalThemeJsonType } from '@/settings/chartThemes/index'
import { option } from './config'
import { CollapseItem, SettingItemBox } from '@/components/Pages/ChartItemSetting'

const props = defineProps({
  optionData: {
    type: Object as PropType<GlobalThemeJsonType>,
    required: true
  }
})

//自定义左侧style
const leftStyle:any = {
   width:"100px"
}
const seriesList = computed(() => {
  return props.optionData.series
})
</script>
<style scoped lang="scss">
 .input-tag{
  width: 200px;
 }
</style>