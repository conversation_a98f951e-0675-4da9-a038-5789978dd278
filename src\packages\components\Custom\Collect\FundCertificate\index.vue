<template>
  <div :style="`width:${w}px;height:${h}px;`" class="fund-certificate c-white">
    <!--标题-->
    <fund-head
      :title="option.value.titleValue"
      :type="option.value.tableDataType"
      :style="`width:${w}px`">
    </fund-head>
    <!--内容-->
    <div class="table-wrap" style="{
      background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
      border: 1px solid;
      border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
      padding: 15px 20px 10px;
      overflow: hidden;
    }">
      <div class="scroll-wrap" :style="`height:${option.value.scrollHeight?option.value.scrollHeight:305}px;`" style="{
        overflow: hidden;
      }">
        <vue3-seamless-scroll
          class="scroll"
          style="{
          /* 必需要设置合适的高,因为他的原理是往列表最后添加列表重复元素，所以这个组件高不要大于其内容原先的最大高度 */
            height: 390px;
            min-width: 450px;
            overflow: hidden;
          }"
          v-model='option.value.isScroll'
          :list="totalData.value"
          :step="0.5"
          :hover="true"
          :limit-scroll-num="2"
          :wheel="true"
          >
            <div class='tag-item' v-for="(item, index) in totalData.value" :key="index" style="{
              min-height: 40px;
              color: #26BDFF;
              display: flex;
              justify-content: space-between;
              text-align: center;
            }">
              <div v-for="(zheng, zhengindex) in item" :key="zhengindex" class="m-b-20">
                <div class="item-img"
                :style="{
                  width: '250px',
                }"
                 style="{
                  width: 250px;
                  height: 178px;
                  padding: 5px;
                  border: 1px solid #02A9FF80;
                  margin-bottom: 5px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                }">
                  <img
                  :style="{
                    width: '240px',
                    height: '168px'
                  }"
                   style="{
                    height: 168px;
                  }" :src="zheng.img" alt="">
                </div>
                <div>{{zheng.name}}</div>
              </div>
            </div>
        </vue3-seamless-scroll>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { getSevenDateRange, divide } from '@/utils'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import  { Vue3SeamlessScroll} from 'vue3-seamless-scroll'
import  FundHead from '@/components/FundHead/index.vue'
import { apiBackgroundFundSupervisionBigShieldDetailsQualification } from '@/api/path'
import { StorageEnum } from '@/enums/storageEnum'
import { getLocalStorage } from '@/utils'
import { useRoute } from 'vue-router'
import dataJson from './data.json'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'

import fanyingyizhong1 from "@/assets/images/newDemo/zizhi/fanyingyizhong (1).png"
import fanyingyizhong2 from "@/assets/images/newDemo/zizhi/fanyingyizhong (2).png"
import fanyingyizhong3 from "@/assets/images/newDemo/zizhi/fanyingyizhong (3).png"
import fanyingyizhong4 from "@/assets/images/newDemo/zizhi/fanyingyizhong (4).png"
import fanyingerzhong1 from "@/assets/images/newDemo/zizhi/fanyingerzhong (1).png"
import fanyingerzhong2 from "@/assets/images/newDemo/zizhi/fanyingerzhong (2).png"
import fanyingerzhong3 from "@/assets/images/newDemo/zizhi/fanyingerzhong (3).png"
import fanyingerzhong4 from "@/assets/images/newDemo/zizhi/fanyingerzhong (4).png"
import shicaoyizhong1 from "@/assets/images/newDemo/zizhi/shicaoyizhong (1).png"
import shicaoyizhong2 from "@/assets/images/newDemo/zizhi/shicaoyizhong (2).png"
import shicaoyizhong3 from "@/assets/images/newDemo/zizhi/shicaoyizhong (3).png"
import shicaoyizhong4 from "@/assets/images/newDemo/zizhi/shicaoyizhong (4).png"
const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);
// 配置
// 配置
const route = useRoute()
console.log('打印一下props', props.chartConfig)
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option),
  totalPrice: 0.00,
  dateType: 'month',
  channelId: Number(route.query.channel_id),
  orgId: Number(route.query.org_id),
  selectValue: 0
})
console.log("option222", option);

const scrollHeight = props.chartConfig.option.scrollHeight ? props.chartConfig.option.scrollHeight : 305
 

// let organizationId
// const info = getLocalStorage(StorageEnum.GO_SYSTEM_STORE)
// if (info) {
//   organizationId = info.userInfo.use_org
// } else {
//   organizationId = Number(route.query.organization_id);
// }
const chartEditStore = useChartEditStore()
const selectValue = chartEditStore.getControlGlobalConfigByKey('ChooseTree')

const allData = {
  fanyingyizhong: [
    [{
      name: "餐饮服务",
      img: fanyingyizhong1
    },
    {
      name: "税务登记证",
      img: fanyingyizhong2
    }],
    [{
      name: "卫生许可证",
      img: fanyingyizhong3
    },
    {
      name: "营业执照",
      img: fanyingyizhong4
    }]
  ],
  fanyingerzhong: [
    [{
      name: "餐饮服务许可证",
      img: fanyingerzhong1
    },
    {
      name: "税务登记证",
      img: fanyingerzhong2
    }],
    [{
      name: "卫生许可证",
      img: fanyingerzhong3
    },
    {
      name: "营业执照",
      img: fanyingerzhong4
    }]
  ],
  shicaoyizhong: [
    [{
      name: "餐饮服务许可证",
      img: shicaoyizhong1
    },
    {
      name: "税务登记证",
      img: shicaoyizhong2
    }],
    [{
      name: "卫生许可证",
      img: shicaoyizhong3
    },
    {
      name: "营业执照",
      img: shicaoyizhong4
    }]
  ]
}

// const totalData = reactive({
//   value: allData.fanyingyizhong
//   })
const totalData = reactive({
  value: [] as any
})

const dateRange = getSevenDateRange(7)

// 留样仪
const getFoodReservedList = async () => {
  const res = await apiBackgroundFundSupervisionBigShieldDetailsQualification({
    org_id: option.selectValue ? option.selectValue : option.orgId,
  })
  if (res && res.data) {
    let arr: any[] = []
    if (res.data.length > 1) {
      res.data.forEach((item: any) => {
        let obj = {
          name: item.qualification_type_alias,
          img: item.image
        }
        arr.push(obj)
        if (arr.length === 2) {
          totalData.value.push([...arr])
          arr = []
        }
      })
      // Handle remaining items if any
      if (arr.length > 0) {
        totalData.value.push([...arr])
      }
    } else if (res.data.length === 1) {
      totalData.value = [
        [
          {
            name: res.data[0].qualification_type_alias,
            img: res.data[0].image,
          }
        ]
      ]
    } else  {
      totalData.value = []
    }
  }
}


// dataset 无法变更条数的补丁
watch(
  () => props.chartConfig.option,
  (newData: any) => {
    console.log("newData有新得值更新", newData);
    option.value = cloneDeep(newData)
  },
  {
    immediate: true,
    deep: true
  }
)

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    const selectValue = chartEditStore.getControlGlobalConfigByKey('ChooseTree')
    if (selectValue?.value === null) {
      option.selectValue = 0
    } else {
      option.selectValue = selectValue?.value
    }
    getFoodReservedList()
  },
  {
    immediate: true,
    deep: true
  }
)


</script>

<style scoped lang="scss">
.fund-certificate {
  .table-wrap{
    background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
    padding: 15px 20px 10px;
    overflow: hidden;
  }
  .scroll-wrap{
    overflow: hidden;
  }
  .scroll {
  /* 必需要设置合适的高,因为他的原理是往列表最后添加列表重复元素，所以这个组件高不要大于其内容原先的最大高度 */
    height: 390px;
    min-width: 450px;
    overflow: hidden;
  }
  
  .tag-item {
    min-width: 479px;
    min-height: 40px;
    display: flex;
    justify-content: space-between;
    text-align: center;
    color: #26BDFF;
    .item-img{
      width: 250px;
      height: 178px;
      padding: 5px;
      border: 1px solid #02A9FF80;
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      img{
        width: 240px;
        height: 168px;
      }
    }
  }

  .flex-4{
    flex: 4;
  }
  .flex-3{
    flex: 3;
  }
  .flex-2{
    flex: 2;
  }
  .flex-1{
    flex: 1;
  }
  .green-color{
    color: #0BF9FE!important;
  }
  .red-color{
    color: #FF4343!important;
  }
  .blue-color{
    color: #26BDFF!important;
  }
  .f-s-28{
    font-size: 28px;
  }
  .f-w-600{
    font-weight: 600;
  }
}
</style>