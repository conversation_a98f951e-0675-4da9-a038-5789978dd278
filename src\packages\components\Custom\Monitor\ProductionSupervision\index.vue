<template>
  <div :style="`width:${w}px;height:${h}px;`" class="production-supervision m-t-10">
    <fund-head :title="option.value.titleValue" :style="`width:${w}px`" 
      @selectHandle="handlerClickArea">
    </fund-head>
    <div class="production-list ps-flex flex-wrap p-t-10" :style="`width:${w}px;height:${contentHeight}px;`"
    v-loading="dataLoading">
    <vue3-seamless-scroll class="scroll"  :list="dataList" :step="0.5"  :style="`height:${contentHeight}px;`" v-model="option.value.isScroll"
    :hover="false" :limit-scroll-num="4" :wheel="true">
      <div v-for="(item, index) in dataList" :key="index" class="list-tag m-l-10 m-t-10">
        <div class="tag-index">{{ index+1 }}</div>
        <div class="tag-name m-l-10">{{ item.name }}</div>
      </div>
    </vue3-seamless-scroll>
    </div>
  
  </div>
</template>
<script lang="ts" setup>
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, watchEffect, onMounted, reactive } from 'vue'
import config from './config'
import { FundHead } from '@/components/FundHead'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { ProductionSupervision } from './index'
import cloneDeep from 'lodash/cloneDeep'
import { apiBackgroundFundSupervisionBigShieldGenerateSupervisionListPost } from '@/api/path'
import { useRoute } from 'vue-router'
import dataJson from './data.json'
import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'
const route = useRoute()

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
// 长宽
const { w, h } = toRefs(props.chartConfig.attr)
const contentHeight = ref((h.value - 42))
console.log("w", w, "h", h);
// 控件的key
const componentKey = ProductionSupervision.key
// 配置
const option = reactive({
  value: cloneDeep(props.chartConfig.option)
})
console.log("option", option);
// 数据列表
const dataList = ref<Array<any>>([])
// 数据加载中
const dataLoading = ref(false)

//  保存的数据
const chartEditStore = useChartEditStore()
// 区域改变
const handlerClickArea = (item: any) => {
  console.log("item", item);
}
// 初始化数据
const initData = async () => {
  dataList.value = cloneDeep(dataJson)
  // getDataList()
}
// 获取数据
const getDataList = async (type?: any) => {
  dataLoading.value = true
  let res = await apiBackgroundFundSupervisionBigShieldGenerateSupervisionListPost({
    channel_id: option.value.selectValue ? option.value.selectValue : Number(route.query.channel_id),
    role_id: Number(route.query.role_id)
  })
  dataLoading.value = false
  if (res && res.code === 0) {
    let data = res.data || []
   if(!data || data.length === 0){
    dataList.value = cloneDeep(dataJson)
   }else{
    dataList.value = cloneDeep(data)
   }
   console.log('dataList.value', dataList.value)
  } else {
    dataList.value = []
  }
}

onMounted(() => {
  initData()
})


watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.value.selectValue) {
        option.value.selectValue = newData.value
        initData()
      } else if (option.value.selectValue !== newData.value) {
        option.value.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

</script>
<style scoped lang="scss">
  .production-supervision {
    .production-list{
      overflow-y: auto;
      border-left: 1px solid #082F50;
      border-right: 1px solid #082F50;
      border-bottom: 1px solid #082F50;
    }

    .list-tag {
      display: inline-flex;
      justify-content: center;
      border-bottom: 1px dashed #082F50;
      padding-bottom: 5px;
      align-items: baseline;

      .tag-name {
        font-size: 16px;
        width: 300px;
        color: #fff;
      }
    }
    .tag-index {
      width: 18px;
      height: 18px;
      line-height: 18px;
      background: #00AAFF;
      color: #fff;
      font-size: 14px;
      text-align: center;
    }
  }
</style>