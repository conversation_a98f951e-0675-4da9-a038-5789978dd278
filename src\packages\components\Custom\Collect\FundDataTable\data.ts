import fanyingyizhong1 from "@/assets/images/newDemo/jiankang/fanyingyizhong1.png"
import fanyingyizhong2 from "@/assets/images/newDemo/jiankang/fanyingyizhong2.png"
import fanyingyizhong3 from "@/assets/images/newDemo/jiankang/fanyingyizhong3.png"
import fanyingyizhong4 from "@/assets/images/newDemo/jiankang/fanyingyizhong4.png"
import fanyingyizhong5 from "@/assets/images/newDemo/jiankang/fanyingyizhong5.png"
import fanyingerzhong1 from "@/assets/images/newDemo/jiankang/fanyingerzhong1.png"
import fanyingerzhong2 from "@/assets/images/newDemo/jiankang/fanyingerzhong2.png"
import fanyingerzhong3 from "@/assets/images/newDemo/jiankang/fanyingerzhong3.png"
import fanyingerzhong4 from "@/assets/images/newDemo/jiankang/fanyingerzhong4.png"
import fanyingerzhong5 from "@/assets/images/newDemo/jiankang/fanyingerzhong5.png"
import fanyingerzhong6 from "@/assets/images/newDemo/jiankang/fanyingerzhong6.png"
import shicaoyizhong1 from "@/assets/images/newDemo/jiankang/shicaoyizhong1.png"
import shicaoyizhong2 from "@/assets/images/newDemo/jiankang/shicaoyizhong2.png"
import shicaoyizhong3 from "@/assets/images/newDemo/jiankang/shicaoyizhong3.png"
import shicaoyizhong4 from "@/assets/images/newDemo/jiankang/shicaoyizhong4.png"
import shicaoyizhong5 from "@/assets/images/newDemo/jiankang/shicaoyizhong5.png"

// 健康证假数据
export const healthCertificateAllData = {
  fanyingyizhong: [
    {
      "name": "王丽娜",
      "number": "410106197505200023",
      "date": "2025/12/9",
      "img": fanyingyizhong1
    },
    {
      "name": "李明华",
      "number": "41010519800101001X",
      "date": "2026/1/3",
      "img": fanyingyizhong2
    },
    {
      "name": "刘俊轩",
      "number": "410181198211110037",
      "date": "2025/12/16",
      "img": fanyingyizhong3
    },
    {
      "name": "周晓琳",
      "number": "410182198803150046",
      "date": "2026/2/2",
      "img": fanyingyizhong4
    },
    {
      "name": "孙浩然",
      "number": "410183199507200054",
      "date": "2026/1/16",
      "img": fanyingyizhong5
    }
  ],
  fanyingerzhong: [
  {
        "name": "李文博",
        "number": "410185199102140070",
        "date": "2025/12/29",
        "img": fanyingerzhong1
    },
    {
        "name": "王雅琪",
        "number": "410184199309300062",
        "date": "2025/12/21",
        "img": fanyingerzhong2
    },
    {
        "name": "陈思颖",
        "number": "410108198706250088",
        "date": "2026/1/29",
        "img": fanyingerzhong3
    },
    {
        "name": "刘浩然",
        "number": "410102199412120095",
        "date": "2025/12/25",
        "img": fanyingerzhong4
    },
    {
        "name": "周志远",
        "number": "410105198107150018",
        "date": "2025/12/30",
        "img": fanyingerzhong5
    },
    {
        "name": "吴子淳",
        "number": "410103199604220023",
        "date": "2026/1/20",
        "img": fanyingerzhong6
    }
  ],
  shicaoyizhong: [
    {
    "name": "尹天佑",
    "number": "410185199302140073",
    "date": "2026/1/29",
    "img": shicaoyizhong1
    },
    {
    "name": "罗梓涵",
    "number": "410108198906250085",
    "date": "2026/1/21",
    "img": shicaoyizhong2
    },
    {
    "name": "汤若琳",
    "number": "410103199804220024",
    "date": "2025/12/27",
    "img": shicaoyizhong3
    },
    {
    "name": "宋一帆",
    "number": "410105198307150016",
    "date": "2025/12/25",
    "img": shicaoyizhong4
    },
    {
    "name": "邹雨馨",
    "number": "410106197808200021",
    "date": "2025/12/18",
    "img": shicaoyizhong5
    }
  ]
}
// 食材信息公示假数据
export const foodInformationAllData = {
  fanyingyizhong: [
    {
    "food": "大米",
    "name": "沈丘聚爱食材供应商",
    "price": "4.5"
    },
    {
    "food": "绿豆",
    "name": "沈丘聚爱食材供应商",
    "price": "4.6"
    },
    {
    "food": "西兰花",
    "name": "沈丘聚爱食材供应商",
    "price": "1.82"
    },
    {
    "food": "猪肉",
    "name": "沈丘聚爱食材供应商",
    "price": "12.3"
    },
    {
    "food": "花菜",
    "name": "沈丘聚爱食材供应商",
    "price": "1.43"
    },
    {
    "food": "面粉",
    "name": "沈丘聚爱食材供应商",
    "price": "3.12"
    },
    {
    "food": "黄豆芽",
    "name": "沈丘聚爱食材供应商",
    "price": "0.4"
    },
    {
    "food": "腐竹",
    "name": "沈丘聚爱食材供应商",
    "price": "12.4"
    },
    {
    "food": "西红柿",
    "name": "沈丘聚爱食材供应商",
    "price": "2.2"
    },
    {
    "food": "土豆",
    "name": "沈丘聚爱食材供应商",
    "price": "1.41"
    },
    {
    "food": "豆腐",
    "name": "沈丘聚爱食材供应商",
    "price": "2.1"
    },
    {
    "food": "青菜",
    "name": "沈丘聚爱食材供应商",
    "price": "0.55"
    },
    {
    "food": "杂粮",
    "name": "沈丘聚爱食材供应商",
    "price": "8.6"
    },
    {
    "food": "白菜",
    "name": "沈丘聚爱食材供应商",
    "price": "0.341"
    },
    {
    "food": "鸡蛋",
    "name": "沈丘聚爱食材供应商",
    "price": "3.8"
    },
    {
    "food": "胡萝卜",
    "name": "沈丘聚爱食材供应商",
    "price": "0.91"
    },
    {
    "food": "绿豆芽",
    "name": "沈丘聚爱食材供应商",
    "price": "0.99"
    },
    {
    "food": "包菜",
    "name": "沈丘聚爱食材供应商",
    "price": "1.5"
    },
    {
    "food": "鸡肉",
    "name": "沈丘聚爱食材供应商",
    "price": "1.98"
    },
    {
    "food": "西葫芦",
    "name": "沈丘聚爱食材供应商",
    "price": "1.045"
    },
    {
    "food": "香菇",
    "name": "沈丘聚爱食材供应商",
    "price": "4.73"
    },
    {
    "food": "猪肚",
    "name": "沈丘聚爱食材供应商",
    "price": "12"
    }
  ],
  fanyingerzhong: [
    {
    "food": "大米",
    "name": "沈丘聚爱食材供应商",
    "price": "4.5"
    },
    {
    "food": "蒜黄",
    "name": "沈丘聚爱食材供应商",
    "price": "4.73"
    },
    {
    "food": "鸡蛋",
    "name": "沈丘聚爱食材供应商",
    "price": "3.8"
    },
    {
    "food": "猪肉",
    "name": "沈丘聚爱食材供应商",
    "price": "12.3"
    },
    {
    "food": "胡萝卜",
    "name": "沈丘聚爱食材供应商",
    "price": "0.91"
    },
    {
    "food": "木耳",
    "name": "沈丘聚爱食材供应商",
    "price": "36"
    },
    {
    "food": "西红柿",
    "name": "沈丘聚爱食材供应商",
    "price": "2.2"
    },
    {
    "food": "绿豆",
    "name": "沈丘聚爱食材供应商",
    "price": "4.6"
    },
    {
    "food": "包菜",
    "name": "沈丘聚爱食材供应商",
    "price": "1.5"
    },
    {
    "food": "芹菜",
    "name": "沈丘聚爱食材供应商",
    "price": "0.72"
    },
    {
    "food": "豆干",
    "name": "沈丘聚爱食材供应商",
    "price": "3.5"
    },
    {
    "food": "上海青",
    "name": "沈丘聚爱食材供应商",
    "price": "0.7"
    },
    {
    "food": "香菇",
    "name": "沈丘聚爱食材供应商",
    "price": "4.73"
    },
    {
    "food": "卤肉",
    "name": "沈丘聚爱食材供应商",
    "price": "10"
    },
    {
    "food": "杏鲍菇",
    "name": "沈丘聚爱食材供应商",
    "price": "2.8"
    },
    {
    "food": "豆角",
    "name": "沈丘聚爱食材供应商",
    "price": "4.1"
    },
    {
    "food": "茄子",
    "name": "沈丘聚爱食材供应商",
    "price": "1.32"
    },
    {
    "food": "豆腐",
    "name": "沈丘聚爱食材供应商",
    "price": "2.1"
    },
    {
    "food": "白菜",
    "name": "沈丘聚爱食材供应商",
    "price": "0.341"
    },
    {
    "food": "面筋",
    "name": "沈丘聚爱食材供应商",
    "price": "4.3"
    },
    {
    "food": "牛肉",
    "name": "沈丘聚爱食材供应商",
    "price": "30"
    },
    {
    "food": "紫菜",
    "name": "沈丘聚爱食材供应商",
    "price": "1.2"
    },
  ],
  shicaoyizhong: [
  {
        "food": "小米",
        "name": "沈丘聚爱食材供应商",
        "price": "8.6"
    },
    {
        "food": "南瓜",
        "name": "沈丘聚爱食材供应商",
        "price": "1.32"
    },
    {
        "food": "胡萝卜",
        "name": "沈丘聚爱食材供应商",
        "price": "0.91"
    },
    {
        "food": "土豆",
        "name": "沈丘聚爱食材供应商",
        "price": "1.41"
    },
    {
        "food": "面粉",
        "name": "沈丘聚爱食材供应商",
        "price": "3.12"
    },
    {
        "food": "卤肉",
        "name": "沈丘聚爱食材供应商",
        "price": "10"
    },
    {
        "food": "蒜苔",
        "name": "沈丘聚爱食材供应商",
        "price": "4.73"
    },
    {
        "food": "猪肉",
        "name": "沈丘聚爱食材供应商",
        "price": "12.3"
    },
    {
        "food": "菠菜",
        "name": "沈丘聚爱食材供应商",
        "price": "0.61"
    },
    {
        "food": "紫菜",
        "name": "沈丘聚爱食材供应商",
        "price": "1.2"
    },
    {
        "food": "鸡蛋",
        "name": "沈丘聚爱食材供应商",
        "price": "3.8"
    },
    {
        "food": "大米",
        "name": "沈丘聚爱食材供应商",
        "price": "4.5"
    },
    {
        "food": "韭菜",
        "name": "沈丘聚爱食材供应商",
        "price": "1.3"
    },
    {
        "food": "绿豆",
        "name": "沈丘聚爱食材供应商",
        "price": "4.6"
    },
    {
        "food": "白菜",
        "name": "沈丘聚爱食材供应商",
        "price": "0.34"
    },
    {
        "food": "豆腐",
        "name": "沈丘聚爱食材供应商",
        "price": "2.1"
    },
    {
        "food": "韭菜",
        "name": "沈丘聚爱食材供应商",
        "price": "1.3"
    },
    {
        "food": "牛肉",
        "name": "沈丘聚爱食材供应商",
        "price": "30"
    },
    {
        "food": "红豆",
        "name": "沈丘聚爱食材供应商",
        "price": "1.8"
    },
    {
        "food": "绿豆",
        "name": "沈丘聚爱食材供应商",
        "price": "4.6"
    },
    {
        "food": "香菇",
        "name": "沈丘聚爱食材供应商",
        "price": "4.7"
    },
    {
        "food": "青椒",
        "name": "沈丘聚爱食材供应商",
        "price": "1.2"
    }
  ]
}