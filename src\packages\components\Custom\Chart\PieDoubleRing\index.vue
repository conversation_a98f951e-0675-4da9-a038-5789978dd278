<template>
  <div :style="`width:${w}px;height:${h}px;`" class="pie-income c-white">
    <!--标题-->
    <fund-head :title="option.value.titleValue" :style="`width:${w}px`">
    </fund-head>
    <div class="pie-wrap">
      <div class="pie-chart" :style="`width:${h - 75}px;height:${h - 75}px;`">
        <v-chart ref="vChartRef" :init-options="initOptions" :theme="themeColor" :option="option.value.chartOpts"
          autoresize></v-chart>
      </div>
      <div class="pie-data">
        <div class="pie-data-item" v-for="(item, index) in labelList" :key="index">
          <div :class="['item-name', item.class]">{{ item.name }}</div>
          <div class="item-price">￥{{ item.value }} <span>（{{ item.rate }}%）</span></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import VChart from 'vue-echarts'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import { use, graphic } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { DatasetComponent, GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { useChartDataFetch } from '@/hooks'
import FundHead from '@/components/FundHead/index.vue'
import { apiBackgroundFundSupervisionBigShieldDietLiability } from '@/api/path'
import { useRoute } from 'vue-router'
import { formatDivideAmount } from '@/utils'
import { DIET_LABEL_LIST } from './data'
import { mealExpensesScreen } from './data'
const route = useRoute()

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);

use([DatasetComponent, CanvasRenderer, LineChart, GridComponent, TooltipComponent, LegendComponent])
const chartEditStore = useChartEditStore()

const option = reactive({
  value: props.chartConfig.option,
  dateType: 'month',
  channelId: Number(route.query.channel_id),
  orgId: Number(route.query.org_id),
  selectValue: 0
})
// 数据列表
const labelList = ref<Array<any>>(cloneDeep(DIET_LABEL_LIST))
// 根据图表基本配置选择类型获取对应数据列表和chartOptions
function getLabelListBytableDataType(tableDataType:string){
  switch (tableDataType) {
    case 'mealExpensesScreen':
      // props.chartConfig.option.chartOpts.title.subtext = '合计营收'
      // props.chartConfig.option.chartOpts.series[0].data = cloneDeep(DIET_OPTION_DATA)
      return cloneDeep(DIET_LABEL_LIST)
    default:
      return []
  }
}
// 获取数据
const getTableData = async (type?: any) => {
  console.log('tableDataType的值', option.value.tableDataType);
  if (!option.selectValue) {
    return
  }
  let api: any
  let params: any = {}
  switch (option.value.tableDataType) {
    case 'mealExpensesScreen':
      params = {
        org_list: option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined,
        channel_id: option.channelId,
      }
      api = apiBackgroundFundSupervisionBigShieldDietLiability
    break
  }
  const res = await api(params)
  if (res && res.code === 0) {
    let data = res.data ? res.data : {}
    // chartOptions
    props.chartConfig.option.chartOpts.series[1].data[0].value = data.non_flowing_liability_price_rate || 0 // 非流动负债百分比
    props.chartConfig.option.chartOpts.series[1].data[1].value = (100 - data.non_flowing_liability_price_rate) || 0 // 底部非流动负债百分比

    props.chartConfig.option.chartOpts.series[3].data[0].value = data.flowing_liability_price_rate || 0 // 流动负债百分比
    props.chartConfig.option.chartOpts.series[3].data[1].value = (100 - data.flowing_liability_price_rate) || 0 // 底部流动负债百分比
    
    // 右侧
    labelList.value[0].value = formatDivideAmount(data.non_flowing_liability_price) || 0 // 非流动负债值金额
    labelList.value[0].rate =  data.non_flowing_liability_price_rate || 0 // 非流动负债百分比

    labelList.value[1].value = formatDivideAmount(data.flowing_liability_price) || 0 // 流动负债值金额
    labelList.value[1].rate =  data.flowing_liability_price_rate || 0 // 流动负债百分比

    // 总数
    // let total = divide(data.non_flowing_liability_price + data.flowing_liability_price)
    let allLiabilities = formatDivideAmount(data.non_flowing_liability_price + data.flowing_liability_price)
    props.chartConfig.option.chartOpts.graphic[0].style.text = [
      `{a|￥${allLiabilities}}`,
      `{b|全部负债}`
    ].join('\n')

    // 底部颜色特殊处理
    if (!data.flowing_liability_price && !data.non_flowing_liability_price){
    props.chartConfig!.option!.chartOpts!.series[0]!.itemStyle!.color = '#0BF9FE'
    props.chartConfig!.option!.chartOpts!.series[2]!.itemStyle!.color = '#5B99FF'
    } else {
      props.chartConfig!.option!.chartOpts!.series[0]!.itemStyle!.color = 'rgba(255, 255, 255, 0.1)'
      props.chartConfig!.option!.chartOpts!.series[2]!.itemStyle!.color = 'rgba(255, 255, 255, 0.1)'
    }
  } else {
    // chartOptions
    props.chartConfig.option.chartOpts.series[1].data[0].value = 0 // 非流动负债百分比
    props.chartConfig.option.chartOpts.series[1].data[1].value = 100 // 底部非流动负债百分比

    props.chartConfig.option.chartOpts.series[3].data[0].value = 0 // 流动负债百分比
    props.chartConfig.option.chartOpts.series[3].data[1].value = 100 // 底部流动负债百分比
    
    // 右侧
    labelList.value[0].value = 0 // 非流动负债值金额
    labelList.value[0].rate =  0 // 非流动负债百分比

    labelList.value[1].value = 0 // 流动负债值金额
    labelList.value[1].rate =  0 // 流动负债百分比

    // 总数
    let total = 0.00
    props.chartConfig.option.chartOpts.graphic[0].style.text = [
      `{a|￥${total}}`,
      `{b|全部负债}`
    ].join('\n')

    props.chartConfig!.option!.chartOpts!.series[0]!.itemStyle!.color = '#0BF9FE'
    props.chartConfig!.option!.chartOpts!.series[2]!.itemStyle!.color = '#5B99FF'
  }
}

// 获取筛选后的数据
const getFilteredData = () => {
  if (option.value.tableDataType === 'mealExpensesScreen') {
    const allData = mealExpensesScreen
    let dataKey
    if (option.selectValue) {
      dataKey = option.selectValue
    } else {
      // 如果没有指定 selectValue，使用第一个可用的键
      dataKey = Object.keys(allData)[0] || 'shangyouerzhong'
    }
    const result = allData[dataKey] || []

    props.chartConfig.option.chartOpts.series[1].data[0].value = result[0].rate || 0 // 非流动负债百分比
    props.chartConfig.option.chartOpts.series[1].data[1].value = (100 - result[0].rate) || 0 // 底部非流动负债百分比

    props.chartConfig.option.chartOpts.series[3].data[0].value = result[1].rate || 0 // 流动负债百分比
    props.chartConfig.option.chartOpts.series[3].data[1].value = (100 - result[1].rate) || 0 // 底部流动负债百分比

    return result
  }

  return []
}

const initData = () => {
  if (option.value.tableDataType === 'mealExpensesScreen') {
    // 使用本地数据筛选
    const filteredData = getFilteredData()
    labelList.value = filteredData
  } else {
    getTableData()
  }
}

// 页面加载
onMounted(() => {
  initData()
})

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        getTableData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        getTableData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseSelect'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)


watch(
  () => option.value.tableDataType,
  (newData: any) => {
    if (newData) {
      labelList.value = getLabelListBytableDataType(newData)
    }
  },
  {
    immediate: true,
    deep: true
  }
)
const { vChartRef } = useChartDataFetch(props.chartConfig, useChartEditStore)
</script>

<style scoped lang="scss">
.pie-income {
  .pie-wrap {
    position: relative;
    background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
    display: flex;
    padding: 0 15px;

    .pie-chart {
      flex: 2;
      // background: url('@/assets/images/chart/custom/pie_income_bg.png') no-repeat;
      background-position: 50% 50%;
    }

    .pie-data {
      flex: 1;
      padding-left: 0px;
      // padding-top: 10px;
      // padding-bottom: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .pie-data-item {
        padding: 10px 0px 10px 10px;
        margin-bottom: 20px;
        background: url('@/assets/images/chart/custom/buttom_bg.png') no-repeat;
        background-size: contain;
        .item-price {
          font-size: 16px;
          font-weight: bold;
        }

        .item-name {
          margin-left: 19px;
          position: relative;
          font-size: 14px;
        }

        .item-name::before {
          content: '';
          width: 14px;
          height: 14px;
          position: absolute;
          top: 4px;
          left: -18px;
          border-radius: 4px;
        }

        .green {
          color: #0BF9FE;
        }

        .green::before {
          background: #0BF9FE;
        }

        .skyBlue{
          color: #5B99FF
        }

        .skyBlue::before  {
          background-color: #5B99FF
        }
      }
    }
  }


}
</style>