import { PublicConfigClass } from '@/packages/public'
import { DataForMonth } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
export const includes = ['legend', 'xAxis', 'yAxis', 'grid']
import { chartInitConfig } from '@/settings/designSetting'
import dataJson from './data.json'

// 数据配置
export const option = {
  titleValue: '本月营收',
  dataset: [],
  tableDataType: 'DataForMonth',
}
// 导出配置
export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = DataForMonth.key
  public chartConfig = cloneDeep(DataForMonth)
  public attr = { ...chartInitConfig, w: 288, h: 122, zIndex: -1 }
  // 图表配置项
  public option = cloneDeep(option)
}
