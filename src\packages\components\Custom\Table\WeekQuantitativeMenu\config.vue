<template>
  <div class="">
    <collapse-item name="基础配置" :expanded="true">
      <setting-item-box name="标题" :alone="false">
        <n-input v-model:value ="optionData.titleValue" type="text" placeholder="请输入标题" class="input-tag" show-count :maxlength="12"></n-input>
      </setting-item-box>
      <setting-item-box name="选择类型">
        <n-select
          size="small"
          style="width: 200px"
          v-model:value="optionData.tableDataType"
          :options="selectTableOptions"
          @update:value="selectTableValueHandle"
        />
      </setting-item-box>
    </collapse-item>
  </div> 
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue'
import { PickCreateComponentType } from '@/packages/index.d'
import { GlobalThemeJsonType } from '@/settings/chartThemes/index'
import { option } from './config'
import { CollapseItem, SettingItemBox } from '@/components/Pages/ChartItemSetting'
import dataJson from './data.json'
import { width } from 'dom-helpers'

const props = defineProps({
  optionData: {
    type: Object as PropType<GlobalThemeJsonType>,
    required: true
  },
  chartAttr: {
    type: Object as PropType<Omit<PickCreateComponentType<'attr'>, 'node' | 'conNode'>>,
    required: true
  }
})

// 表格配置选项
const selectTableOptions = [
  {
    label: '食堂带量食谱-本周',
    value: "QuantitativeMenuWeek",
    data: [],
    width: 519,
    height: 830,
    scrollHeight: 600,
    limiScrollNum: 15,
    apiConfig: {
      apiFunction: 'apiBackgroundFundSupervisionBigShieldRationRecipeWeekList',
      apiParams: ['org_id']
    },
    tableSetting: [{
      label: '餐次',
      key: 'meal_type',
      type: 'text',
      flex: 2
    }, {
      label: '食物名称',
      key: 'food_name',
      type: 'text',
      flex: 3
    }, {
      label: '菜品/食材重量',
      key: 'weight_total',
      type: 'text',
      flex: 4
    }, {
      label: '价格',
      key: 'food_price',
      type: 'price',
      flex: 2
    }, {
      label: '配料',
      key: 'ingredient_info',
      type: 'text',
      flex: 5
    }]
  }
]

const selectTableValueHandle = (value: any) => {
  const selectedOption = selectTableOptions.find(item => item.value === value)
  if (selectedOption) {
    props.optionData.titleValue = selectedOption.label
    props.optionData.tableDataType = selectedOption.value
    props.optionData.tableConfig = selectedOption
    props.chartAttr.w = selectedOption.width ? selectedOption.width : 673
    props.chartAttr.h = selectedOption.height ? selectedOption.height : 528
  }
}

// 初始化时设置默认的 tableSetting
const initTableSetting = () => {
  const currentType = props.optionData.tableDataType
  if (currentType) {
    selectTableValueHandle(currentType)
  }
}

// 组件挂载时初始化
initTableSetting()

</script>
<style scoped lang="scss">
 .input-tag{
  width: 200px;
 }
</style>