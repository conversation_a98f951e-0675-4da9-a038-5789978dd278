{"fanyingyizhong": [{"date": "周一", "meal": "早餐", "foodName": "绿豆粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "绿豆", "ingredientWeight": "20g"}, {"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周一", "meal": "早餐", "foodName": "酸辣绿豆芽", "foodWeight": "75", "foodPrice": "2", "childList": [{"ingredientName": "绿豆芽", "ingredientWeight": "60g"}]}, {"date": "周一", "meal": "早餐", "foodName": "蒜蓉西兰花", "foodWeight": "75", "foodPrice": "2", "childList": [{"ingredientName": "西兰花", "ingredientWeight": "60g"}]}, {"date": "周一", "meal": "早餐", "foodName": "葱油饼", "foodWeight": "50", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周一", "meal": "午餐", "foodName": "糊汤面", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "150g"}]}, {"date": "周一", "meal": "午餐", "foodName": "青菜炒腐竹", "foodWeight": "75", "foodPrice": "1", "childList": [{"ingredientName": "青菜", "ingredientWeight": "30g"}, {"ingredientName": "腐竹", "ingredientWeight": "20g"}]}, {"date": "周一", "meal": "午餐", "foodName": "肉丝花菜", "foodWeight": "125", "foodPrice": "3", "childList": [{"ingredientName": "猪肉", "ingredientWeight": "40g"}, {"ingredientName": "花菜", "ingredientWeight": "80g"}]}, {"date": "周一", "meal": "午餐", "foodName": "花卷", "foodWeight": "50", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周一", "meal": "晚餐", "foodName": "八宝粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}, {"ingredientName": "杂粮", "ingredientWeight": "20g"}]}, {"date": "周一", "meal": "晚餐", "foodName": "炒土豆丝", "foodWeight": "100", "foodPrice": "1", "childList": [{"ingredientName": "土豆", "ingredientWeight": "80g"}]}, {"date": "周一", "meal": "晚餐", "foodName": "肉片豆腐炖菜", "foodWeight": "75", "foodPrice": "3", "childList": [{"ingredientName": "猪肉", "ingredientWeight": "20g"}, {"ingredientName": "豆腐", "ingredientWeight": "40g"}, {"ingredientName": "青菜", "ingredientWeight": "10g"}]}, {"date": "周一", "meal": "晚餐", "foodName": "馒头", "foodWeight": "50", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "90g"}]}, {"date": "周二", "meal": "早餐", "foodName": "大米粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周二", "meal": "早餐", "foodName": "炒青菜", "foodWeight": "75", "foodPrice": "1.5", "childList": [{"ingredientName": "青菜", "ingredientWeight": "60g"}]}, {"date": "周二", "meal": "早餐", "foodName": "手撕包菜", "foodWeight": "100", "foodPrice": "1.5", "childList": [{"ingredientName": "包菜", "ingredientWeight": "80g"}]}, {"date": "周二", "meal": "早餐", "foodName": "馅饼", "foodWeight": "125", "foodPrice": "1.5", "childList": [{"ingredientName": "猪肉", "ingredientWeight": "40g"}, {"ingredientName": "面粉", "ingredientWeight": "70g"}]}, {"date": "周二", "meal": "午餐", "foodName": "大米粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周二", "meal": "午餐", "foodName": "土豆鸡块", "foodWeight": "100", "foodPrice": "2", "childList": [{"ingredientName": "土豆", "ingredientWeight": "50g"}, {"ingredientName": "鸡肉", "ingredientWeight": "40g"}]}, {"date": "周二", "meal": "午餐", "foodName": "香菇炒青菜", "foodWeight": "125", "foodPrice": "1.5", "childList": [{"ingredientName": "香菇", "ingredientWeight": "30g"}, {"ingredientName": "青菜", "ingredientWeight": "80g"}]}, {"date": "周二", "meal": "午餐", "foodName": "肉片汤", "foodWeight": "250", "foodPrice": "2", "childList": [{"ingredientName": "猪肉", "ingredientWeight": "40g"}]}, {"date": "周二", "meal": "晚餐", "foodName": "小米粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "小米", "ingredientWeight": "80g"}]}, {"date": "周二", "meal": "晚餐", "foodName": "西葫芦炒肉", "foodWeight": "125", "foodPrice": "2.5", "childList": [{"ingredientName": "西葫芦", "ingredientWeight": "60g"}, {"ingredientName": "猪肉", "ingredientWeight": "40g"}]}, {"date": "周二", "meal": "晚餐", "foodName": "白菜豆腐", "foodWeight": "100", "foodPrice": "1", "childList": [{"ingredientName": "白菜", "ingredientWeight": "50g"}, {"ingredientName": "豆腐", "ingredientWeight": "40g"}]}, {"date": "周二", "meal": "晚餐", "foodName": "千层饼", "foodWeight": "50", "foodPrice": "1", "childList": [{"ingredientName": "面粉", "ingredientWeight": "90g"}]}, {"date": "周三", "meal": "早餐", "foodName": "胡辣汤", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "20g"}, {"ingredientName": "配料", "ingredientWeight": "70g"}]}, {"date": "周三", "meal": "早餐", "foodName": "五香卤蛋", "foodWeight": "75", "foodPrice": "2", "childList": [{"ingredientName": "鸡蛋", "ingredientWeight": "70g"}]}, {"date": "周三", "meal": "早餐", "foodName": "清炒土豆丝", "foodWeight": "125", "foodPrice": "1.5", "childList": [{"ingredientName": "土豆", "ingredientWeight": "100g"}]}, {"date": "周三", "meal": "早餐", "foodName": "油条", "foodWeight": "50", "foodPrice": "1", "childList": [{"ingredientName": "面粉", "ingredientWeight": "90g"}]}, {"date": "周三", "meal": "午餐", "foodName": "鸡蛋刀削面", "foodWeight": "300", "foodPrice": "1", "childList": [{"ingredientName": "鸡蛋", "ingredientWeight": "50g"}, {"ingredientName": "面粉", "ingredientWeight": "150g"}]}, {"date": "周三", "meal": "午餐", "foodName": "胡萝卜炒肉", "foodWeight": "75", "foodPrice": "2", "childList": [{"ingredientName": "胡萝卜", "ingredientWeight": "40g"}, {"ingredientName": "猪肉", "ingredientWeight": "25g"}]}, {"date": "周三", "meal": "午餐", "foodName": "青菜炒香菇", "foodWeight": "75", "foodPrice": "1", "childList": [{"ingredientName": "青菜", "ingredientWeight": "40g"}, {"ingredientName": "香菇", "ingredientWeight": "25g"}]}, {"date": "周三", "meal": "午餐", "foodName": "肚丝汤", "foodWeight": "250", "foodPrice": "1", "childList": [{"ingredientName": "猪肚", "ingredientWeight": "50g"}]}, {"date": "周三", "meal": "晚餐", "foodName": "鸡蛋稀饭", "foodWeight": "300", "foodPrice": "1", "childList": [{"ingredientName": "鸡蛋", "ingredientWeight": "50g"}, {"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周三", "meal": "晚餐", "foodName": "肉片花菜", "foodWeight": "75", "foodPrice": "2.5", "childList": [{"ingredientName": "猪肉", "ingredientWeight": "20g"}, {"ingredientName": "花菜", "ingredientWeight": "35g"}]}, {"date": "周三", "meal": "晚餐", "foodName": "炒平菇", "foodWeight": "125", "foodPrice": "1", "childList": [{"ingredientName": "平菇", "ingredientWeight": "110g"}]}, {"date": "周三", "meal": "晚餐", "foodName": "馒头", "foodWeight": "50", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周四", "meal": "早餐", "foodName": "八宝粥", "foodWeight": "300", "foodPrice": "1", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}, {"ingredientName": "杂粮", "ingredientWeight": "20g"}]}, {"date": "周四", "meal": "早餐", "foodName": "豆芽炒面筋", "foodWeight": "75", "foodPrice": "1.5", "childList": [{"ingredientName": "黄豆芽", "ingredientWeight": "20g"}, {"ingredientName": "面筋", "ingredientWeight": "30g"}]}, {"date": "周四", "meal": "早餐", "foodName": "炒茄瓜", "foodWeight": "125", "foodPrice": "1.5", "childList": [{"ingredientName": "茄瓜", "ingredientWeight": "110g"}]}, {"date": "周四", "meal": "早餐", "foodName": "馒头", "foodWeight": "50", "foodPrice": "1", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周四", "meal": "午餐", "foodName": "大米粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周四", "meal": "午餐", "foodName": "鱼香肉丝", "foodWeight": "125", "foodPrice": "2", "childList": [{"ingredientName": "猪肉", "ingredientWeight": "40g"}, {"ingredientName": "胡萝卜", "ingredientWeight": "20g"}, {"ingredientName": "木耳", "ingredientWeight": "10g"}]}, {"date": "周四", "meal": "午餐", "foodName": "番茄炒鸡蛋", "foodWeight": "125", "foodPrice": "1.5", "childList": [{"ingredientName": "番茄", "ingredientWeight": "60g"}, {"ingredientName": "鸡蛋", "ingredientWeight": "40g"}]}, {"date": "周四", "meal": "午餐", "foodName": "豆腐汤", "foodWeight": "250", "foodPrice": "1", "childList": [{"ingredientName": "豆腐", "ingredientWeight": "60g"}]}, {"date": "周四", "meal": "晚餐", "foodName": "大米粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周四", "meal": "晚餐", "foodName": "水煮肉片", "foodWeight": "75", "foodPrice": "2.5", "childList": [{"ingredientName": "猪肉", "ingredientWeight": "40g"}]}, {"date": "周四", "meal": "晚餐", "foodName": "炝炒生菜", "foodWeight": "125", "foodPrice": "1", "childList": [{"ingredientName": "生菜", "ingredientWeight": "110g"}]}, {"date": "周四", "meal": "晚餐", "foodName": "馒头", "foodWeight": "50", "foodPrice": "1", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周五", "meal": "早餐", "foodName": "红豆粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "红豆", "ingredientWeight": "20g"}, {"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周五", "meal": "早餐", "foodName": "清炒冬瓜", "foodWeight": "125", "foodPrice": "1.5", "childList": [{"ingredientName": "冬瓜", "ingredientWeight": "105g"}]}, {"date": "周五", "meal": "早餐", "foodName": "黄豆芽炒肉", "foodWeight": "75", "foodPrice": "1.5", "childList": [{"ingredientName": "黄豆芽", "ingredientWeight": "40g"}, {"ingredientName": "猪肉", "ingredientWeight": "20g"}]}, {"date": "周五", "meal": "早餐", "foodName": "鲜肉包子", "foodWeight": "50", "foodPrice": "1.5", "childList": [{"ingredientName": "猪肉", "ingredientWeight": "20g"}, {"ingredientName": "面粉", "ingredientWeight": "25g"}]}, {"date": "周五", "meal": "午餐", "foodName": "牛肉拉面", "foodWeight": "300", "foodPrice": "2", "childList": [{"ingredientName": "牛肉", "ingredientWeight": "100g"}, {"ingredientName": "面粉", "ingredientWeight": "150g"}]}, {"date": "周五", "meal": "午餐", "foodName": "卤鸡腿", "foodWeight": "75", "foodPrice": "1.5", "childList": [{"ingredientName": "鸡腿", "ingredientWeight": "65g"}]}, {"date": "周五", "meal": "午餐", "foodName": "炒青菜", "foodWeight": "125", "foodPrice": "1", "childList": [{"ingredientName": "青菜", "ingredientWeight": "110g"}]}, {"date": "周五", "meal": "午餐", "foodName": "馒头", "foodWeight": "50", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}], "fanyingerzhong": [{"date": "周一", "meal": "早餐", "foodName": "玉米糁", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "玉米", "ingredientWeight": "80g"}]}, {"date": "周一", "meal": "早餐", "foodName": "白菜豆腐", "foodWeight": "100", "foodPrice": "2", "childList": [{"ingredientName": "白菜", "ingredientWeight": "40g"}, {"ingredientName": "豆腐", "ingredientWeight": "50g"}]}, {"date": "周一", "meal": "早餐", "foodName": "酸辣绿豆芽", "foodWeight": "100", "foodPrice": "2", "childList": [{"ingredientName": "绿豆芽", "ingredientWeight": "90g"}]}, {"date": "周一", "meal": "早餐", "foodName": "油卷", "foodWeight": "50", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周一", "meal": "午餐", "foodName": "米饭", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周一", "meal": "午餐", "foodName": "蒜黄炒鸡蛋", "foodWeight": "100", "foodPrice": "1", "childList": [{"ingredientName": "蒜黄", "ingredientWeight": "40g"}, {"ingredientName": "鸡蛋", "ingredientWeight": "50g"}]}, {"date": "周一", "meal": "午餐", "foodName": "鱼香肉丝", "foodWeight": "80", "foodPrice": "3", "childList": [{"ingredientName": "猪肉", "ingredientWeight": "30g"}, {"ingredientName": "胡萝卜", "ingredientWeight": "20g"}, {"ingredientName": "木耳", "ingredientWeight": "10g"}]}, {"date": "周一", "meal": "午餐", "foodName": "西红柿鸡蛋汤", "foodWeight": "250", "foodPrice": "0.5", "childList": [{"ingredientName": "西红柿", "ingredientWeight": "30g"}, {"ingredientName": "鸡蛋", "ingredientWeight": "30g"}]}, {"date": "周一", "meal": "晚餐", "foodName": "绿豆粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "绿豆", "ingredientWeight": "20g"}, {"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周一", "meal": "晚餐", "foodName": "包菜炒肉", "foodWeight": "100", "foodPrice": "3", "childList": [{"ingredientName": "包菜", "ingredientWeight": "40g"}, {"ingredientName": "猪肉", "ingredientWeight": "50g"}]}, {"date": "周一", "meal": "晚餐", "foodName": "芹菜炒豆干", "foodWeight": "80", "foodPrice": "1", "childList": [{"ingredientName": "芹菜", "ingredientWeight": "30g"}, {"ingredientName": "豆干", "ingredientWeight": "40g"}]}, {"date": "周一", "meal": "晚餐", "foodName": "馒头", "foodWeight": "60", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "50g"}]}, {"date": "周二", "meal": "早餐", "foodName": "大米粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周二", "meal": "早餐", "foodName": "炒菠菜", "foodWeight": "80", "foodPrice": "1.5", "childList": [{"ingredientName": "菠菜", "ingredientWeight": "70g"}]}, {"date": "周二", "meal": "早餐", "foodName": "红萝卜炒鸡蛋", "foodWeight": "100", "foodPrice": "1.5", "childList": [{"ingredientName": "胡萝卜", "ingredientWeight": "40g"}, {"ingredientName": "鸡蛋", "ingredientWeight": "50g"}]}, {"date": "周二", "meal": "早餐", "foodName": "炸菜角", "foodWeight": "120", "foodPrice": "1.5", "childList": [{"ingredientName": "豆角", "ingredientWeight": "50g"}]}, {"date": "周二", "meal": "午餐", "foodName": "羊肉烩面", "foodWeight": "300", "foodPrice": "2", "childList": [{"ingredientName": "羊肉", "ingredientWeight": "80g"}, {"ingredientName": "面粉", "ingredientWeight": "150g"}]}, {"date": "周二", "meal": "午餐", "foodName": "豆角炒肉", "foodWeight": "100", "foodPrice": "2", "childList": [{"ingredientName": "豆角", "ingredientWeight": "40g"}, {"ingredientName": "猪肉", "ingredientWeight": "50g"}]}, {"date": "周二", "meal": "午餐", "foodName": "酸辣白菜", "foodWeight": "90", "foodPrice": "0.5", "childList": [{"ingredientName": "白菜", "ingredientWeight": "80g"}]}, {"date": "周二", "meal": "午餐", "foodName": "馒头", "foodWeight": "70", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "60g"}]}, {"date": "周二", "meal": "晚餐", "foodName": "鸡蛋稀饭", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "鸡蛋", "ingredientWeight": "30g"}, {"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周二", "meal": "晚餐", "foodName": "烧上海青", "foodWeight": "90", "foodPrice": "1.5", "childList": [{"ingredientName": "上海青", "ingredientWeight": "80g"}]}, {"date": "周二", "meal": "晚餐", "foodName": "香菇肉片", "foodWeight": "100", "foodPrice": "2.5", "childList": [{"ingredientName": "香菇", "ingredientWeight": "30g"}, {"ingredientName": "猪肉", "ingredientWeight": "50g"}]}, {"date": "周二", "meal": "晚餐", "foodName": "炸馍干", "foodWeight": "50", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周三", "meal": "早餐", "foodName": "胡辣汤", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "150g"}, {"ingredientName": "配料", "ingredientWeight": "50g"}]}, {"date": "周三", "meal": "早餐", "foodName": "清炒黄豆芽", "foodWeight": "80", "foodPrice": "1.5", "childList": [{"ingredientName": "黄豆芽", "ingredientWeight": "70g"}]}, {"date": "周三", "meal": "早餐", "foodName": "芥菜丝", "foodWeight": "90", "foodPrice": "2", "childList": [{"ingredientName": "芥菜", "ingredientWeight": "80g"}]}, {"date": "周三", "meal": "早餐", "foodName": "油条", "foodWeight": "50", "foodPrice": "1", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周三", "meal": "午餐", "foodName": "卤面", "foodWeight": "300", "foodPrice": "1", "childList": [{"ingredientName": "卤肉", "ingredientWeight": "80g"}, {"ingredientName": "面粉", "ingredientWeight": "150g"}]}, {"date": "周三", "meal": "午餐", "foodName": "杏鲍菇炒肉", "foodWeight": "80", "foodPrice": "2", "childList": [{"ingredientName": "杏鲍菇", "ingredientWeight": "30g"}, {"ingredientName": "猪肉", "ingredientWeight": "40g"}]}, {"date": "周三", "meal": "午餐", "foodName": "豆角烧茄子", "foodWeight": "90", "foodPrice": "1", "childList": [{"ingredientName": "豆角", "ingredientWeight": "30g"}, {"ingredientName": "茄子", "ingredientWeight": "50g"}]}, {"date": "周三", "meal": "午餐", "foodName": "西湖牛肉羹", "foodWeight": "110", "foodPrice": "1", "childList": [{"ingredientName": "牛肉", "ingredientWeight": "30g"}, {"ingredientName": "豆腐", "ingredientWeight": "40g"}, {"ingredientName": "香菇", "ingredientWeight": "20g"}]}, {"date": "周三", "meal": "晚餐", "foodName": "大米红枣粥", "foodWeight": "300", "foodPrice": "1", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}, {"ingredientName": "红枣", "ingredientWeight": "20g"}]}, {"date": "周三", "meal": "晚餐", "foodName": "韭菜炒千张", "foodWeight": "80", "foodPrice": "2.5", "childList": [{"ingredientName": "韭菜", "ingredientWeight": "30g"}, {"ingredientName": "千张", "ingredientWeight": "40g"}]}, {"date": "周三", "meal": "晚餐", "foodName": "蒜苗炒肉", "foodWeight": "90", "foodPrice": "1", "childList": [{"ingredientName": "蒜苗", "ingredientWeight": "40g"}, {"ingredientName": "猪肉", "ingredientWeight": "40g"}]}, {"date": "周三", "meal": "晚餐", "foodName": "葱油饼", "foodWeight": "50", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周四", "meal": "早餐", "foodName": "八宝粥", "foodWeight": "300", "foodPrice": "1", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}, {"ingredientName": "杂粮", "ingredientWeight": "20g"}]}, {"date": "周四", "meal": "早餐", "foodName": "清炒茄瓜", "foodWeight": "80", "foodPrice": "1.5", "childList": [{"ingredientName": "茄瓜", "ingredientWeight": "70g"}]}, {"date": "周四", "meal": "早餐", "foodName": "蒜蓉西兰花", "foodWeight": "100", "foodPrice": "1.5", "childList": [{"ingredientName": "西兰花", "ingredientWeight": "90g"}]}, {"date": "周四", "meal": "早餐", "foodName": "素包子", "foodWeight": "50", "foodPrice": "1", "childList": [{"ingredientName": "青菜", "ingredientWeight": "30g"}]}, {"date": "周四", "meal": "午餐", "foodName": "大米粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周四", "meal": "午餐", "foodName": "土豆烧鸡块", "foodWeight": "120", "foodPrice": "2", "childList": [{"ingredientName": "土豆", "ingredientWeight": "40g"}, {"ingredientName": "鸡肉", "ingredientWeight": "70g"}]}, {"date": "周四", "meal": "午餐", "foodName": "烧茄子", "foodWeight": "100", "foodPrice": "1.5", "childList": [{"ingredientName": "茄子", "ingredientWeight": "90g"}]}, {"date": "周四", "meal": "午餐", "foodName": "酸辣肚丝汤", "foodWeight": "250", "foodPrice": "1", "childList": [{"ingredientName": "猪肚", "ingredientWeight": "50g"}]}, {"date": "周四", "meal": "晚餐", "foodName": "黑米粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "黑米", "ingredientWeight": "80g"}]}, {"date": "周四", "meal": "晚餐", "foodName": "酸辣土豆丝", "foodWeight": "80", "foodPrice": "1", "childList": [{"ingredientName": "土豆", "ingredientWeight": "70g"}]}, {"date": "周四", "meal": "晚餐", "foodName": "韭菜烧鸭血", "foodWeight": "110", "foodPrice": "2.5", "childList": [{"ingredientName": "韭菜", "ingredientWeight": "30g"}, {"ingredientName": "鸭血", "ingredientWeight": "70g"}]}, {"date": "周四", "meal": "晚餐", "foodName": "馒头", "foodWeight": "50", "foodPrice": "1", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周五", "meal": "早餐", "foodName": "小米南瓜粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "南瓜", "ingredientWeight": "30g"}, {"ingredientName": "小米", "ingredientWeight": "80g"}]}, {"date": "周五", "meal": "早餐", "foodName": "萝卜丝炒粉条", "foodWeight": "120", "foodPrice": "1.5", "childList": [{"ingredientName": "白萝卜", "ingredientWeight": "50g"}, {"ingredientName": "粉丝", "ingredientWeight": "60g"}]}, {"date": "周五", "meal": "早餐", "foodName": "洋葱拌木耳", "foodWeight": "100", "foodPrice": "1.5", "childList": [{"ingredientName": "洋葱", "ingredientWeight": "40g"}, {"ingredientName": "木耳", "ingredientWeight": "50g"}]}, {"date": "周五", "meal": "早餐", "foodName": "鸡蛋煎饼", "foodWeight": "50", "foodPrice": "1.5", "childList": [{"ingredientName": "鸡蛋", "ingredientWeight": "30g"}, {"ingredientName": "面粉", "ingredientWeight": "20g"}]}, {"date": "周五", "meal": "午餐", "foodName": "三丁捞面", "foodWeight": "300", "foodPrice": "1.5", "childList": [{"ingredientName": "猪肉", "ingredientWeight": "80g"}, {"ingredientName": "胡萝卜", "ingredientWeight": "50g"}, {"ingredientName": "土豆", "ingredientWeight": "50g"}, {"ingredientName": "面粉", "ingredientWeight": "150g"}]}, {"date": "周五", "meal": "午餐", "foodName": "白灼生菜", "foodWeight": "90", "foodPrice": "1", "childList": [{"ingredientName": "生菜", "ingredientWeight": "80g"}]}, {"date": "周五", "meal": "午餐", "foodName": "红烧鱼块", "foodWeight": "110", "foodPrice": "2", "childList": [{"ingredientName": "鱼", "ingredientWeight": "100g"}]}, {"date": "周五", "meal": "午餐", "foodName": "蛋花汤", "foodWeight": "250", "foodPrice": "0.5", "childList": [{"ingredientName": "鸡蛋", "ingredientWeight": "30g"}]}], "shicaoyizhong": [{"date": "周一", "meal": "早餐", "foodName": "小米南瓜粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "小米", "ingredientWeight": "60g"}, {"ingredientName": "南瓜", "ingredientWeight": "40g"}]}, {"date": "周一", "meal": "早餐", "foodName": "炒胡萝卜丝", "foodWeight": "90", "foodPrice": "2", "childList": [{"ingredientName": "胡萝卜", "ingredientWeight": "80g"}]}, {"date": "周一", "meal": "早餐", "foodName": "酸辣土豆丝", "foodWeight": "110", "foodPrice": "2", "childList": [{"ingredientName": "土豆", "ingredientWeight": "100g"}]}, {"date": "周一", "meal": "早餐", "foodName": "千层饼", "foodWeight": "50", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周一", "meal": "午餐", "foodName": "炸酱面", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "卤肉", "ingredientWeight": "80g"}, {"ingredientName": "面粉", "ingredientWeight": "150g"}]}, {"date": "周一", "meal": "午餐", "foodName": "蒜苔炒肉", "foodWeight": "110", "foodPrice": "2.5", "childList": [{"ingredientName": "蒜苔", "ingredientWeight": "40g"}, {"ingredientName": "猪肉", "ingredientWeight": "60g"}]}, {"date": "周一", "meal": "午餐", "foodName": "蒜蓉菠菜", "foodWeight": "90", "foodPrice": "1", "childList": [{"ingredientName": "菠菜", "ingredientWeight": "80g"}]}, {"date": "周一", "meal": "午餐", "foodName": "紫菜蛋花汤", "foodWeight": "300", "foodPrice": "1", "childList": [{"ingredientName": "紫菜", "ingredientWeight": "20g"}, {"ingredientName": "鸡蛋", "ingredientWeight": "30g"}]}, {"date": "周一", "meal": "晚餐", "foodName": "大米粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周一", "meal": "晚餐", "foodName": "韭菜炒鸡蛋", "foodWeight": "100", "foodPrice": "1", "childList": [{"ingredientName": "韭菜", "ingredientWeight": "40g"}, {"ingredientName": "鸡蛋", "ingredientWeight": "50g"}]}, {"date": "周一", "meal": "晚餐", "foodName": "回锅肉", "foodWeight": "90", "foodPrice": "3", "childList": [{"ingredientName": "猪肉", "ingredientWeight": "80g"}]}, {"date": "周一", "meal": "晚餐", "foodName": "馒头", "foodWeight": "50", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周二", "meal": "早餐", "foodName": "绿豆粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "绿豆", "ingredientWeight": "60g"}, {"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周二", "meal": "早餐", "foodName": "白菜豆腐", "foodWeight": "100", "foodPrice": "1.5", "childList": [{"ingredientName": "白菜", "ingredientWeight": "40g"}, {"ingredientName": "豆腐", "ingredientWeight": "50g"}]}, {"date": "周二", "meal": "早餐", "foodName": "蚝油冬瓜", "foodWeight": "90", "foodPrice": "1.5", "childList": [{"ingredientName": "冬瓜", "ingredientWeight": "80g"}]}, {"date": "周二", "meal": "早餐", "foodName": "油饼", "foodWeight": "50", "foodPrice": "1.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周二", "meal": "午餐", "foodName": "大米粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周二", "meal": "午餐", "foodName": "西红柿炒鸡蛋", "foodWeight": "110", "foodPrice": "2", "childList": [{"ingredientName": "西红柿", "ingredientWeight": "40g"}, {"ingredientName": "鸡蛋", "ingredientWeight": "50g"}]}, {"date": "周二", "meal": "午餐", "foodName": "青椒炒肉", "foodWeight": "100", "foodPrice": "2", "childList": [{"ingredientName": "青椒", "ingredientWeight": "40g"}, {"ingredientName": "猪肉", "ingredientWeight": "50g"}]}, {"date": "周二", "meal": "午餐", "foodName": "黄瓜汤", "foodWeight": "60", "foodPrice": "0.5", "childList": [{"ingredientName": "黄瓜", "ingredientWeight": "50g"}]}, {"date": "周二", "meal": "晚餐", "foodName": "鸡蛋稀饭", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "鸡蛋", "ingredientWeight": "30g"}, {"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周二", "meal": "晚餐", "foodName": "粉丝娃娃菜", "foodWeight": "100", "foodPrice": "1.5", "childList": [{"ingredientName": "粉丝", "ingredientWeight": "40g"}, {"ingredientName": "娃娃菜", "ingredientWeight": "50g"}]}, {"date": "周二", "meal": "晚餐", "foodName": "蘑菇炒肉", "foodWeight": "120", "foodPrice": "2.5", "childList": [{"ingredientName": "蘑菇", "ingredientWeight": "40g"}, {"ingredientName": "猪肉", "ingredientWeight": "70g"}]}, {"date": "周二", "meal": "晚餐", "foodName": "油卷", "foodWeight": "50", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周三", "meal": "早餐", "foodName": "小米南瓜粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "小米", "ingredientWeight": "60g"}, {"ingredientName": "南瓜", "ingredientWeight": "40g"}]}, {"date": "周三", "meal": "早餐", "foodName": "炒小白菜", "foodWeight": "100", "foodPrice": "1.5", "childList": [{"ingredientName": "小白菜", "ingredientWeight": "90g"}]}, {"date": "周三", "meal": "早餐", "foodName": "黄豆芽炒肉", "foodWeight": "110", "foodPrice": "2", "childList": [{"ingredientName": "黄豆芽", "ingredientWeight": "50g"}, {"ingredientName": "猪肉", "ingredientWeight": "50g"}]}, {"date": "周三", "meal": "早餐", "foodName": "葱油饼", "foodWeight": "50", "foodPrice": "1", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周三", "meal": "午餐", "foodName": "肉丝炝锅面", "foodWeight": "300", "foodPrice": "1.5", "childList": [{"ingredientName": "猪肉", "ingredientWeight": "80g"}, {"ingredientName": "面粉", "ingredientWeight": "150g"}]}, {"date": "周三", "meal": "午餐", "foodName": "麻辣土豆片", "foodWeight": "100", "foodPrice": "1", "childList": [{"ingredientName": "土豆", "ingredientWeight": "90g"}]}, {"date": "周三", "meal": "午餐", "foodName": "小炒肉", "foodWeight": "90", "foodPrice": "2", "childList": [{"ingredientName": "猪肉", "ingredientWeight": "80g"}]}, {"date": "周三", "meal": "午餐", "foodName": "黑菜豆腐汤", "foodWeight": "80", "foodPrice": "0.5", "childList": [{"ingredientName": "木耳", "ingredientWeight": "20g"}, {"ingredientName": "豆腐", "ingredientWeight": "50g"}]}, {"date": "周三", "meal": "晚餐", "foodName": "鸡蛋稀饭", "foodWeight": "300", "foodPrice": "1", "childList": [{"ingredientName": "鸡蛋", "ingredientWeight": "30g"}, {"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周三", "meal": "晚餐", "foodName": "粉丝娃娃菜", "foodWeight": "90", "foodPrice": "1", "childList": [{"ingredientName": "粉丝", "ingredientWeight": "40g"}, {"ingredientName": "娃娃菜", "ingredientWeight": "40g"}]}, {"date": "周三", "meal": "晚餐", "foodName": "蘑菇炒肉", "foodWeight": "110", "foodPrice": "2.5", "childList": [{"ingredientName": "蘑菇", "ingredientWeight": "40g"}, {"ingredientName": "猪肉", "ingredientWeight": "60g"}]}, {"date": "周三", "meal": "晚餐", "foodName": "油卷", "foodWeight": "50", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周四", "meal": "早餐", "foodName": "胡辣汤", "foodWeight": "300", "foodPrice": "1", "childList": [{"ingredientName": "面粉", "ingredientWeight": "150g"}, {"ingredientName": "配料", "ingredientWeight": "50g"}]}, {"date": "周四", "meal": "早餐", "foodName": "酸辣白菜", "foodWeight": "110", "foodPrice": "1.5", "childList": [{"ingredientName": "白菜", "ingredientWeight": "100g"}]}, {"date": "周四", "meal": "早餐", "foodName": "青椒豆腐丝", "foodWeight": "90", "foodPrice": "1.5", "childList": [{"ingredientName": "青椒", "ingredientWeight": "40g"}, {"ingredientName": "豆腐", "ingredientWeight": "40g"}]}, {"date": "周四", "meal": "早餐", "foodName": "馒头", "foodWeight": "50", "foodPrice": "1", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周四", "meal": "午餐", "foodName": "大米粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}]}, {"date": "周四", "meal": "午餐", "foodName": "红烧鸡腿", "foodWeight": "110", "foodPrice": "2.5", "childList": [{"ingredientName": "鸡腿", "ingredientWeight": "100g"}]}, {"date": "周四", "meal": "午餐", "foodName": "清炒土豆丝", "foodWeight": "120", "foodPrice": "1", "childList": [{"ingredientName": "土豆", "ingredientWeight": "110g"}]}, {"date": "周四", "meal": "午餐", "foodName": "西红柿鸡蛋汤", "foodWeight": "70", "foodPrice": "1", "childList": [{"ingredientName": "西红柿", "ingredientWeight": "30g"}, {"ingredientName": "鸡蛋", "ingredientWeight": "30g"}]}, {"date": "周四", "meal": "晚餐", "foodName": "大米红枣粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}, {"ingredientName": "红枣", "ingredientWeight": "20g"}]}, {"date": "周四", "meal": "晚餐", "foodName": "花菜炒肉", "foodWeight": "90", "foodPrice": "2.5", "childList": [{"ingredientName": "花菜", "ingredientWeight": "40g"}, {"ingredientName": "猪肉", "ingredientWeight": "50g"}]}, {"date": "周四", "meal": "晚餐", "foodName": "芹菜炒豆干", "foodWeight": "110", "foodPrice": "1.5", "childList": [{"ingredientName": "芹菜", "ingredientWeight": "40g"}, {"ingredientName": "豆干", "ingredientWeight": "50g"}]}, {"date": "周四", "meal": "晚餐", "foodName": "发面饼", "foodWeight": "50", "foodPrice": "0.5", "childList": [{"ingredientName": "面粉", "ingredientWeight": "40g"}]}, {"date": "周五", "meal": "早餐", "foodName": "八宝粥", "foodWeight": "300", "foodPrice": "0.5", "childList": [{"ingredientName": "大米", "ingredientWeight": "80g"}, {"ingredientName": "杂粮", "ingredientWeight": "20g"}]}, {"date": "周五", "meal": "早餐", "foodName": "韭菜炒鸡蛋", "foodWeight": "120", "foodPrice": "1.5", "childList": [{"ingredientName": "韭菜", "ingredientWeight": "50g"}, {"ingredientName": "鸡蛋", "ingredientWeight": "60g"}]}, {"date": "周五", "meal": "早餐", "foodName": "清炒蘑菇", "foodWeight": "90", "foodPrice": "1.5", "childList": [{"ingredientName": "蘑菇", "ingredientWeight": "80g"}]}, {"date": "周五", "meal": "早餐", "foodName": "素包子", "foodWeight": "50", "foodPrice": "1.5", "childList": [{"ingredientName": "青菜", "ingredientWeight": "30g"}]}, {"date": "周五", "meal": "午餐", "foodName": "卤面", "foodWeight": "300", "foodPrice": "1.5", "childList": [{"ingredientName": "卤肉", "ingredientWeight": "80g"}, {"ingredientName": "面粉", "ingredientWeight": "150g"}]}, {"date": "周五", "meal": "午餐", "foodName": "糖醋里脊", "foodWeight": "100", "foodPrice": "2", "childList": [{"ingredientName": "猪肉", "ingredientWeight": "90g"}]}, {"date": "周五", "meal": "午餐", "foodName": "炒菠菜", "foodWeight": "110", "foodPrice": "1", "childList": [{"ingredientName": "菠菜", "ingredientWeight": "100g"}]}, {"date": "周五", "meal": "午餐", "foodName": "西湖牛肉羹", "foodWeight": "80", "foodPrice": "0.5", "childList": [{"ingredientName": "牛肉", "ingredientWeight": "30g"}, {"ingredientName": "豆腐", "ingredientWeight": "40g"}, {"ingredientName": "香菇", "ingredientWeight": "20g"}]}]}