<template>
  <div :style="`width:${w}px;height:${h}px;`" class="early-warning-information c-white">
    <!--标题-->
    <fund-head :title="props.chartConfig.option.titleValue" :type="props.chartConfig.option.tableDataType"
      :style="`width:${w}px`">
    </fund-head>
    <div class="early-warning-information-content">
      <div class="item-out" v-for="(item, index) in infoData" :key="index">
        <div class="item-in">
          <div class="item-in-label">{{ item.label }}</div>
          <div :class="['item-in-value', parseInt(item.value) > 0 ? 'red' : 'white']">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { getSevenDateRange, divide } from '@/utils'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import  { Vue3SeamlessScroll} from 'vue3-seamless-scroll'
import { apiBackgroundFundSupervisionBigShieldWarningOverview } from '@/api/path'
import { StorageEnum } from '@/enums/storageEnum'
import { getLocalStorage } from '@/utils'
import { useRoute } from 'vue-router'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import FundHead from '@/components/FundHead/index.vue'
import dayjs from 'dayjs'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);
// 配置
const route = useRoute()
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option),
  channelId: Number(route.query.channel_id)
})
console.log("option222", option);

const totalData = ref<any>({})

const dateRange = getSevenDateRange(7)

// 数据获取
const infoData = ref<any>([
    {
      label: '经营预警',
      key: 'materials_warn_count',
      value: 2
    },
    {
      label: '出入库预警',
      key: 'materials_risk_warn_count',
      value: 1
    },
    {
      label: '证件合同预警',
      key: 'documents_overdue_warn_count',
      value: 0
    },
    {
      label: '其他预警',
      key: 'other_warn_count',
      value: 0
    }
  ])
const getData = async () => {
  const res: any = await apiBackgroundFundSupervisionBigShieldWarningOverview({
    channel_id: Number(route.query.channel_id),
    role_id: Number(route.query.role_id),
    date: dayjs().format('YYYY-MM')
  })
  if (res.code === 0) {
    console.log("res", res);
    let data = res.data || {}
    infoData.value.forEach((item: any) => {
      switch (item.key) {
        case 'materials_warn_count':
          item.value = data.profit_warn_count + data.materials_warn_count || 0
          break
        case 'materials_risk_warn_count':
          item.value = data.materials_risk_warn_count || 0
          break
        case 'documents_overdue_warn_count':
          item.value = data.documents_overdue_warn_count + data.contract_overdue_warn_count || 0
          break
        default:
          item.value = data.other_warn_count || 0
          break

      }
    })
  }
}

// 页面加载
onMounted(() => {
  console.log("onMounted")
  // getData()
})

// dataset 无法变更条数的补丁
watch(
  () => props.chartConfig.option,
  (newData: any) => {
    console.log("newData有新得值更新", newData);
    option.value = cloneDeep(newData)
  },
  {
    immediate: true,
    deep: true
  }
)


</script>

<style scoped lang="scss">
.early-warning-information {
  background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
  border: 1px solid;
  border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
  // padding: 15px 20px 10px;
  overflow: hidden;
  &-content {
    padding: 21px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 16px;

    .item-out, .item-in {
      position: relative;
    }
    .item-out {
      &::before {
        content: "";
        position: absolute;
        top: 0; left: 0;
        width: 6px; height: 6px;
        border-top: 2px solid #06A5FF;
        border-left: 2px solid #06A5FF;
      }
      &::after {
        content: "";
        position: absolute;
        top: 0; right: 0;
        width: 6px; height: 6px;
        border-top: 2px solid #06A5FF;
        border-right: 2px solid #06A5FF;
      }
      .item-in {
        height: 100px;
        border: 1px solid #00A3FF1A;
        background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
        border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
        overflow: hidden;
        padding: 0px 24px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        &::before {
          content: "";
          position: absolute;
          bottom: 0; left: 0;
          width: 6px; height: 6px;
          border-bottom: 2px solid #06A5FF;
          border-left: 2px solid #06A5FF;
        }
        &::after {
          content: "";
          position: absolute;
          bottom: 0; right: 0;
          width: 6px; height: 6px;
          border-bottom: 2px solid #06A5FF;
          border-right: 2px solid #06A5FF;
        }
        &-label {
          color: #FFF;
          font-size: 22px;
          font-weight: 4 00;
        }
        &-value {
          font-size: 36px;
          font-weight: 700;
        }
        .red {
          color: #FF4343;
        }
        .white {
          color: #fff;
        }
      }
    }
  }
}
</style>