<template>
  <div :style="`width:${w}px;height:${h}px;`" class="pie-income c-white">
    <!--标题-->
    <fund-head :title="option.value.titleValue" :style="`width:${w}px`">
    </fund-head>
    <div class="pie-wrap">
      <div class="pie-chart" :style="`width:100%;height:${h - 150}px;`">
        <v-chart ref="vChartRef" :init-options="initOptions" :theme="themeColor" :option="option.value.chartOpts"
          autoresize></v-chart>
      </div>
      <div class="pie-data">
        <div class="pie-data-item" v-for="(item, index) in labelList" :key="index">
          <div :class="['item-name', item.class]">{{ item.name }}</div>
          <!-- <div class="item-price">￥{{ item.value }}</div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, PropType, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import VChart from 'vue-echarts'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { DatasetComponent, GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { useChartDataFetch } from '@/hooks'
import FundHead from '@/components/FundHead/index.vue'
import { apiBackgroundFundSupervisionBigShieldDietIncomeDistribution } from '@/api/path'
import { useRoute } from 'vue-router'
import { divide, formatDivideAmount } from '@/utils'
import { DIET_LABEL_LIST, DIET_OPTION_DATA} from './data'
const route = useRoute()

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);

use([DatasetComponent, CanvasRenderer, LineChart, GridComponent, TooltipComponent, LegendComponent])
const chartEditStore = useChartEditStore()

const option = reactive({
  value: props.chartConfig.option,
  dateType: 'month',
  channelId: Number(route.query.channel_id),
  orgId: Number(route.query.org_id),
  selectValue: 0
})
// 数据列表
const labelList = ref<Array<any>>(cloneDeep(DIET_LABEL_LIST))
// 根据图表基本配置选择类型获取对应数据列表和chartOptions
function getLabelListBytableDataType(tableDataType:string){
  switch (tableDataType) {
    case 'DietDashboard':
      props.chartConfig.option.chartOpts.title.text = "合计营收 ¥" + 0.00
      props.chartConfig.option.chartOpts.series[0].data = cloneDeep(DIET_OPTION_DATA)
      return cloneDeep(DIET_LABEL_LIST)
    default:
      return []
  }
}
// 获取数据
const getTableData = async (type?: any) => {
  console.log('tableDataType的值', option.value.tableDataType);
  if (!option.selectValue) {
    return
  }
  let api: any
  let params: any = {}
  let total :number;
  switch (option.value.tableDataType) {
    case 'DietDashboard':
      params = {
        org_list: option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined,
        channel_id: option.channelId,
      }
      api = apiBackgroundFundSupervisionBigShieldDietIncomeDistribution
    break
  }
  const res = await api(params)
  if (res && res.code === 0) {
    let data = res.data ? res.data : {}
    switch (option.value.tableDataType) {
       // 膳食大屏
      case 'DietDashboard':
        labelList.value = labelList.value.map(item => {
          return {
            ...item,
            value: formatDivideAmount(data[item.key]) ?? 0
          }
        })
        props.chartConfig.option.chartOpts.series[0].data.forEach((seriesItem: any) => {
          let thisType = seriesItem.data_type
          seriesItem.value = divide(data[thisType]) ?? 0
          // 大于0显示在上 否则在下
          seriesItem.label.position = data[thisType] >= 0 ? 'top' : 'bottom'
        })
        total = formatDivideAmount(Object.values(data).reduce((acc:number, val:any) => acc + val, 0))
        props.chartConfig.option.chartOpts.title.text ="合计营收 ¥ " + total
      break
    }
  } else {
      props.chartConfig.option.chartOpts.series[0].data.forEach(item=>{
        item.value = 0
      })
      labelList.value.forEach((item: any) => {
        item.value = 0
      })
      props.chartConfig.option.chartOpts.title.text = "合计营收 ¥ " + 0.00
  }
}

// 页面加载
onMounted(() => {
  getTableData()
})

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        getTableData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        getTableData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)
watch(
  () => option.value.tableDataType,
  (newData: any) => {
    if (newData) {
      labelList.value = getLabelListBytableDataType(newData)
    }
  },
  {
    immediate: true,
    deep: true
  }
)
const { vChartRef } = useChartDataFetch(props.chartConfig, useChartEditStore)
</script>

<style scoped lang="scss">
.pie-income {
  .pie-wrap {
    position: relative;
    background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
    display: flex;
    flex-direction: column;
    padding: 0 15px;

    .pie-chart {
      // flex: 1;
      // background: url('@/assets/images/chart/custom/pie_income_bg.png') no-repeat;
      background-position: 50% 50%;
    }

    .pie-data {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      padding-left: 20px;
      padding-bottom: 10px;

      .pie-data-item {
        width: 25%;
        margin-bottom: 10px;
        .item-price {
          font-size: 16px;
          font-weight: bold;
        }

        .item-name {
          margin-left: 19px;
          position: relative;
          font-size: 14px;
        }

        .item-name::before {
          content: '';
          width: 14px;
          height: 14px;
          position: absolute;
          top: 4px;
          left: -18px;
          border-radius: 4px;
        }

        .green {
          color: #17E9AD;
        }

        .green::before {
          background: #17E9AD;
        }

        .yellow {
          color: #FFD364;
        }

        .yellow::before {
          background: #FFD364;
        }

        .red {
          color: #EF7647;
        }

        .red::before {
          background: #EF7647;
        }

        .blue {
          color: #0296D9;
        }

        .blue::before {
          background: #0296D9;
        }

        .green-light {
          color: #93EF04;
        }

        .green-light::before {
          background: #93EF04;
        }

        .purple {
          color: #4B04E4;
        }

        .purple::before {
          background: #4B04E4;
        }

        .grey {
          color: #727C79;
        }

        .grey::before {
          background: #727C79;
        }

        .grey-light {
          color: #5E87B5;
        }

        .grey-light::before {
          background: #5E87B5;
        }

        .darkblue{
          color: #23588D;
        }

        .darkblue::before {
          background-color: #23588D;
        }

        .brightcyan{
          color: #0ADDE8
        }

        .brightcyan::before{
          background-color: #0ADDE8
        }

        .purple{
          color: #6456FF;
        }

        .purple::before{
          background-color: #6456FF;
        }

        .skyBlue{
          color: #38B2FF
        }

        .skyBlue::before  {
          background-color: #38B2FF
        }
      }
    }
  }


}
</style>