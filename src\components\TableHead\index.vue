<template>
  <div v-show="show">
    <div class="fund-head">
      <div class="head-title m-l-12" v-if="isShowHeadTitle">
        {{ title }}
      </div>
      <div class="right-wrap" v-if="dateTypeOptions.length">
        <div
          v-for="option in dateTypeOptions"
          :key="option"
          :class="['btn-item', props.dateType === option ? 'active-bg' : 'un-active-bg']"
          @click="selectDateType(option)"
        >
          {{ visibleDateOptions.find(item => item.value === option)?.label }}
        </div>
      </div>
      <!-- 右边需要啥，自己加独立的right-wrap控制显示，不要影响别人的 -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, PropType } from 'vue'
const emit = defineEmits(['selectHandle'])

const props = defineProps({
  dateType: {
    type: String,
    default: 'month'
  },
  show: {
    type: Boolean,
    default: true
  },
  isShowHeadTitle: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: ''
  },
  // 新增：控制显示哪些时间类型的数组
  dateTypeOptions: {
    type: Array as PropType<string[]>,
    default: () => [],
    validator: (value: string[]) => {
      const validTypes = ['week', 'month', 'year']
      return value.every(type => validTypes.includes(type))
    }
  }
})

const visibleDateOptions = [
  { label: '本周', value: 'week' },
  { label: '本月', value: 'month' },
  { label: '本年', value: 'year' }
]

const selectDateType = (type: string) => {
  emit('selectHandle', type)
}

onMounted(() => {
})


</script>

<style lang="scss" scoped>
.fund-head {
  width: 100%;
  height: 50px;
  position: relative;
  background-image: url('@/assets/images/chart/custom/fund_head_bg.png');
  background-repeat: no-repeat;

  display: flex;
  justify-content: space-between;
  .head-title {
    margin-left: 40px;
    line-height: 50px;
    font-weight: 700;
    color: #fff;
    font-size: 24px;
    font-style: italic;
    letter-spacing: 4px;
  }
  .right-wrap{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .btn-item{
      width: 100px;
      height: 38px;
      line-height: 38px;
      text-align: center;
      font-size: 18px;
      cursor: pointer;
    }
    .active-bg{
      background-image: url('@/assets/images/chart/custom/active_btn_bg.png');
    }
    .un-active-bg{
      background-image: url('@/assets/images/chart/custom/un_active_btn_bg.png');
    }
  }
}
</style>