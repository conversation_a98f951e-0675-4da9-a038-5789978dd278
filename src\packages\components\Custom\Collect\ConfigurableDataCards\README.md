# 可配置数据卡片组件

## 概述

`ConfigurableDataCards` 是一个高度可配置的数据展示组件，支持多种显示模式，可用于展示金额统计、数据统计等信息。组件具有良好的扩展性和灵活的配置选项。

## 功能特性

- ✅ **双显示模式**: 支持金额显示模式和统计显示模式
- ✅ **配置化数据**: 通过配置文件轻松修改显示内容
- ✅ **预设模板**: 提供常用的预设模板
- ✅ **灵活样式**: 支持自定义颜色、字体、图标等
- ✅ **响应式布局**: 自动适应不同数量的数据项
- ✅ **向后兼容**: 新增配置参数提供默认值，确保旧配置正常工作

## 显示模式

### 1. 金额显示模式 (amount)
适用于展示金额相关的统计数据，如：
- 食堂物资金额合计
- 收入支出统计
- 财务数据汇总

特点：
- 显示大号金额数字
- 支持环比数据显示
- 支持趋势图标（上升/下降）

### 2. 统计显示模式 (stats)
适用于展示一般统计数据，如：
- 设备状态统计
- 样品数量统计
- 人员统计

特点：
- 紧凑的横向布局
- 图标+标签+数值的组合
- 适合显示状态类信息

## 配置选项

### 基础配置
```typescript
{
  titleValue: string,           // 组件标题
  displayMode: 'amount' | 'stats', // 显示模式
  dateTypeOptions: string[],    // 时间类型选项
  dataset: DataItem[]           // 数据项数组
}
```

### 数据项结构
```typescript
interface DataItem {
  title: string,        // 项目标题
  value: string,        // 显示值
  comparison?: string,  // 对比值（仅金额模式）
  icon?: string         // 图标URL（可选）
}
```

### 样式配置
```typescript
{
  // 卡片配置
  cardConfig: {
    cardWidth: string,    // 卡片宽度，如 "30%" 或 "200px"
    cardStyle: object     // 自定义卡片样式
  },
  
  // 颜色配置
  iconColors: string[],     // 图标颜色数组
  amountColors: string[],   // 金额颜色数组（金额模式）
  statsValueColors: string[], // 统计值颜色数组（统计模式）
  
  // 字体配置
  titleFontSize: string,    // 标题字体大小
  amountFontSize: string,   // 金额字体大小
  statsValueFontSize: string, // 统计值字体大小
  
  // 分隔线配置
  showDivider: boolean,     // 是否显示分隔线
  dividerStyle: object      // 分隔线样式
}
```

## 预设模板

### 1. 食堂物资金额合计 (canteen_materials)
```json
{
  "titleValue": "食堂物资金额合计",
  "displayMode": "amount",
  "dataset": [
    {
      "title": "采购金额合计",
      "value": "¥ 4680.00",
      "comparison": "+12.5%"
    },
    {
      "title": "入库成本合计", 
      "value": "¥ 4680.00",
      "comparison": "+8.3%"
    },
    {
      "title": "出库金额合计",
      "value": "¥ 4680.00", 
      "comparison": "-2.1%"
    }
  ]
}
```

### 2. 食材留样 (material_samples)
```json
{
  "titleValue": "食材留样",
  "displayMode": "stats",
  "dataset": [
    {
      "title": "设备在线",
      "value": "6 / 8"
    },
    {
      "title": "当日留样品数",
      "value": "56"
    },
    {
      "title": "当日取样品数", 
      "value": "49"
    }
  ]
}
```

## 使用方法

### 1. 在配置界面中使用
1. 选择"可配置数据卡片"组件
2. 在基础配置中选择显示模式
3. 选择预设模板或自定义配置
4. 配置数据项内容
5. 调整样式设置

### 2. 编程方式使用
```vue
<template>
  <ConfigurableDataCards
    :themeSetting="themeSetting"
    :themeColor="themeColor" 
    :chartConfig="chartConfig"
  />
</template>

<script>
import { ConfigurableDataCardsConfig } from './ConfigurableDataCards'

// 创建配置实例
const config = new ConfigurableDataCardsConfig()

// 自定义配置
config.option.displayMode = 'amount'
config.option.dataset = [
  {
    title: '自定义项目',
    value: '1000',
    comparison: '+5%'
  }
]
</script>
```

## 扩展指南

### 添加新的预设模板
在 `config.vue` 中的 `templateData` 对象中添加新模板：

```typescript
const templateData = {
  // 现有模板...
  
  new_template: {
    titleValue: '新模板标题',
    displayMode: 'stats',
    dataset: [
      // 模板数据...
    ]
  }
}
```

### 添加新的样式配置
1. 在 `config.ts` 的 `option` 中添加新配置项
2. 在 `index.vue` 中添加对应的样式计算方法
3. 在 `config.vue` 中添加配置界面

### 添加新的显示模式
1. 在 `index.vue` 中添加新的模板结构
2. 在配置中添加对应的样式配置
3. 更新类型定义和配置界面

## 注意事项

- 确保图标URL可访问，否则会显示默认图标
- 金额格式化会自动处理大数值（超过10万显示为万元）
- 环比数据支持正负值，会自动显示对应的趋势图标
- 组件会自动适应数据项数量，建议不超过5个项目以保证显示效果

## 更新日志

### v1.0.0
- 初始版本发布
- 支持金额和统计两种显示模式
- 提供基础的配置选项和预设模板
