<template>
  <div :style="`width:${w}px;height:${h}px;`" class="choose-select m-t-10">
    <div>{{ option.value.titleValue }}</div>
    <n-select
      :options="option.value.dataList"
      :style="`width:${w}px;`"
      :default-value="option.value.defaultValue"
      class="my-selecct"
      @change="handlerChooseChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs } from 'vue'
import config from './config'
import { useChartDataFetch } from '@/hooks'
import { CreateComponentType } from '@/packages/index.d'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { isPreview } from '@/utils'
import isObject from 'lodash/isObject'
import cloneDeep from 'lodash/cloneDeep'
import { controlGlobalConfigType } from '@/store/modules/chartEditStore/chartEditStore.d'
import { ChooseSelect } from './index'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})

const {
  borderColor
} = toRefs(props.chartConfig.option)
//
const { w, h } = toRefs(props.chartConfig.attr)
console.log('w', w, 'h', h)
// 配置
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option)
})
// 用户数据
const chartEditStore = useChartEditStore()
let key = ChooseSelect.key //自己的 key
// 选择改变
const handlerChooseChange = value => {
  console.log('chooseSelect', value)
  let tag = chartEditStore.getControlGlobalConfigByKey(key)
  console.log('chooseSelect tag', tag);
  let saveConfigItem: controlGlobalConfigType = tag ? tag :{
    compontentKey: key,
    type: 'choose',
    value: value,
    paramsKey: '',
    bindIds: []
  }
  if (tag) {
    saveConfigItem.value = value
  }
  console.log('chooseSelect tag', saveConfigItem);
  chartEditStore.setControlGlobalConfig(saveConfigItem)
  console.log("chooseSelect tag111", chartEditStore.getControlGlobalConfig);
  
}

// dataset 无法变更条数的补丁
watch(
  () => props.chartConfig.option,
  (newData: any) => {
    console.log('newData有新得值更新', newData)
    option.value = cloneDeep(newData)
  },
  {
    immediate: true,
    deep: true
  }
)
</script>

<style scoped lang="scss">
@include deep() {
  .n-base-selection-label {
    height: v-bind('h + "px"');
    display: flex;
    align-items: center;
  }
  .n-base-selection .n-base-selection-label {
    height: v-bind('h + "px"');
    display: flex;
    align-items: center;
    border-radius: 2px !important;
    border: 1px solid v-bind('borderColor') !important;
    .n-base-selection-input__content {
      color: v-bind('borderColor') !important;
    }
    .n-base-suffix__arrow {
      color: v-bind('borderColor') !important;
    }
  }
  .n-base-select-menu .n-base-select-option.n-base-select-option--selected{
    color: v-bind('borderColor') !important;
  }
}
.my-selecct {
  :deep(n-base-selection-label) {
    display: flex;
    align-items: center;
  }
  :deep(.n-base-selection .n-base-selection-label) {
    display: flex;
    align-items: center;
    border-radius: 2px !important;
    border: 1px solid v-bind('borderColor') !important;
    .n-base-selection-input__content {
      color: v-bind('borderColor') !important;
    }
    .n-base-suffix__arrow {
      color: v-bind('borderColor') !important;
    }
  }
  :deep(.n-base-select-menu .n-base-select-option.n-base-select-option--selected){
    color: v-bind('borderColor') !important;
  }
}
</style>
