import { PublicConfigClass } from '@/packages/public'
import { FundDataTable } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import head1Image from '@/assets/images/chart/custom/ic_head_1.png'
import head2Image from '@/assets/images/chart/custom/ic_head_2.png'
import head3Image from '@/assets/images/chart/custom/ic_head_3.png'
import checkGreenImage from '@/assets/images/chart/custom/ic_check_green.png'
import checkOrangeImage from '@/assets/images/chart/custom/ic_check_orange.png'
export const includes = ['legend', 'xAxis', 'yAxis', 'grid']
import { chartInitConfig } from '@/settings/designSetting'
import dataJson from './data.json'

// 数据配置
export const option = {
  titleValue: '收支利润排行',
  isScroll: true , // 是否滚动，默认是
  scrollHeight: 305,
  checkGreenImage: checkGreenImage,
  checkOrangeImage: checkOrangeImage,
  dataset: dataJson.profitList.monthData,
  tableDataType: 'profit',
}
// 导出配置
export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = FundDataTable.key
  public chartConfig = cloneDeep(FundDataTable)
  public attr = { ...chartInitConfig, w: 596, h: 440, zIndex: -1 }
  // 图表配置项
  public option = cloneDeep(option)
}
