import { ConfigType, PackagesCategoryEnum, ChartFrameEnum } from '@/packages/index.d'
import { CustomCategoryEnum, CustomCategoryEnumName } from '../../index.d'

export const ScrollMergeTable: ConfigType = {
  key: 'ScrollMergeTable',
  chartKey: 'VScrollMergeTable',
  conKey: 'VCScrollMergeTable',
  title: '滚动合并表格2',
  category: CustomCategoryEnum.Collect,
  categoryName: CustomCategoryEnumName.Collect,
  package: PackagesCategoryEnum.CUSTOM,
  chartFrame: ChartFrameEnum.COMMON,
  image: 'scroll_merge_table.png'
}
