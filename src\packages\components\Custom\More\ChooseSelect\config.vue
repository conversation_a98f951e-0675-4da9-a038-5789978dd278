<template>
  <div class="">
    <collapse-item name="基础配置" :expanded="true">
      <div class="m-b-20 ps-flex">
        <div class="w-80">颜色：</div>
        <n-color-picker class="w-200" size="small" :modes="['hex']" v-model:value="optionData.borderColor"></n-color-picker>
      </div>
      <div>静态选项配置：</div>
      <div class="m-t-10" v-for="(item, index) in optionData.dataList" :key="index">
        <div class="ps-flex">
          <div class="w-80">选项名称：</div>
          <n-input v-model:value="item.label" type="text" placeholder="请输入" class="input-tag" maxlength="20"
            show-count></n-input>
        </div>
        <div class="ps-flex m-t-5">
          <div class="w-80">选项值id：</div>
          <n-input v-model:value="item.value" type="text" placeholder="请输入" class="input-tag" maxlength="20"
            show-count></n-input>
        </div>
      </div>
      <div class="m-t-20">选项的默认值：</div>
      <div class="ps-flex">
        <n-input v-model:value="optionData.defaultValue" type="text" placeholder="请输入" class="input-tag m-l-80">
        </n-input>
      </div>
      <div class="m-t-20">设置调用接口获取数据时的key值</div>
      <div class="ps-flex">
        <n-input v-model:value="optionData.paramsId" type="text" placeholder="请输入" class="input-tag m-l-80">
        </n-input>
        <div>
          <n-popover trigger="hover">
            <template #trigger>
              <n-icon size="30" :component="HelpOutlineIcon" />
            </template>
            <span>这里配置调用服务器获取数据时的params key 值，不使用调用服务器方式获取默认数据的不用配置</span>
          </n-popover>
        </div>
      </div>

    </collapse-item>
    <collapse-item name="数据关联配置" :expanded="true">
      <div>
        <div>请选择关联的组件</div>
        <n-select v-model:value="optionData.relativeIds" :options="relativeList" multiple @change="handlerChooseChange" />
      </div>
    </collapse-item>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed, ref } from 'vue'
import { GlobalThemeJsonType } from '@/settings/chartThemes/index'
import { option } from './config'
import { CollapseItem, SettingItemBox } from '@/components/Pages/ChartItemSetting'
import { usePackagesStore } from '@/store/modules/packagesStore/packagesStore'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { controlGlobalConfigType } from '@/store/modules/chartEditStore/chartEditStore.d'
import { icon } from "@/plugins"
import { ChooseSelect } from './index'
const props = defineProps({
  optionData: {
    type: Object as PropType<GlobalThemeJsonType>,
    required: true
  }
})
//自定义左侧style
const leftStyle: any = {
  width: "100px"
}
// 图标
const { HelpOutlineIcon } = icon.ionicons5

const seriesList = computed(() => {
  return props.optionData.series
})
// 关联列表
let relativeList = ref<any[]>()
let key = ChooseSelect.key //自己的 key
const chartEditStore = useChartEditStore()
const componentList = chartEditStore.getComponentList


console.log("chooseDataList", componentList, chartEditStore)
let getRelativeList = () => {
  let list: any[] = []
  if (componentList && Array.isArray(componentList)) {
    componentList.forEach(item => {
      let tag = {
        label: item.chartConfig.title,
        value: item.key
      }
      if (key !== item.key) { // 如果不是自己。就添加
        list.push(tag)
      }

    }
    )
  }
  return list
}
relativeList.value = getRelativeList()
console.log("relativeList", relativeList.value)

// 选择监听
const handlerChooseChange = (dataList: []) => {
  console.log("handlerChooseChange", dataList);
  // 保存的数据项
  let saveConfigItem: controlGlobalConfigType = {
    compontentKey: key,
    type: 'choose',
    value: [],
    paramsKey: '',
    bindIds: dataList
  }
  chartEditStore.setControlGlobalConfig(saveConfigItem)
}

</script>
<style scoped lang="scss">
.input-tag {
  width: 200px;
}
</style>