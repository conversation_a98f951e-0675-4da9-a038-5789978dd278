<template>
  <div v-show="show">
    <div class="fund-head">
      <div class="head-title m-l-12" v-if="isShowHeadTitle">
        {{ title }}
      </div>
      <div class="right-wrap"
        v-if="type === 'profit' || type === 'costExpenditureDetails' || type === 'canteenConsumption' || type === 'incomeSituation' || type === 'incomeRanking'">
        <div v-if="type === 'canteenConsumption'"
          :class="['btn-item', props.timeType === 'week' ? 'active-bg' : 'un-active-bg']"
          @click="selectDateType('week')">本周</div>
        <div :class="['btn-item', props.timeType === 'month' ? 'active-bg' : 'un-active-bg']"
          @click="selectDateType('month')">本月</div>
        <div v-if="type !== 'canteenConsumption'"
          :class="['btn-item', props.timeType === 'year' ? 'active-bg' : 'un-active-bg']"
          @click="selectDateType('year')">本年</div>
      </div>
      <div class="right-wrap" v-if="type === 'incomeSituation-month' || type === 'incomeSituation-year'">
        <div :class="['btn-item', 'active-bg']" v-if="type === 'incomeSituation-month'">本月</div>
        <div v-if="type === 'incomeSituation-year'" :class="['btn-item', 'active-bg']">本年</div>
      </div>
      <div class="right-wrap" v-if="type === 'perCapita'">
        <div class="m-r-20">
          <img src="@/assets/images/chart/custom/up_icon.png" alt="" srcset="">
          <span class="m-l-5">上升</span>
        </div>
        <div class="m-r-20">
          <img src="@/assets/images/chart/custom/down_icon.png" alt="" srcset="">
          <span class="m-l-5">下降</span>
        </div>
      </div>
      <div class="right-wrap" v-if="type === 'proportionOfRawMaterial'">
        <div class="proportionOfRawMaterial-item m-r-40">自营</div>
        <div class="proportionOfRawMaterial-item m-r-20 yellow">承包</div>
      </div>
      <div class="right-wrap"
        v-if="type === 'revenueExpenditure' || type === 'accountant' || type === 'revenueExpenditure-meal' || type === 'revenueExpenditureForRoleId'">
        <div class="revenueExpenditure-item m-r-60">收入</div>
        <div class="revenueExpenditure-item m-r-20 yellow2">支出</div>
      </div>
      <div class="right-wrap" v-if="type === 'income'">
        <div class="revenueExpenditure-item m-r-20">收入</div>
      </div>
      <div class="right-wrap" v-if="type === 'express'">
        <div class="revenueExpenditure-item m-r-20 yellow2">支出</div>
      </div>
      <div class="right-wrap" v-if="type === 'yearProfit'">
        <div class="yearProfit-item m-r-60">今年</div>
        <div class="yearProfit-item m-r-20 yellow2">去年</div>
      </div>
      <div class="right-wrap" v-if="type === 'monitor'">
        <div class="ps-flex" v-for="(item, index) in imgList" :key="index" :name="item.name"
          @click="handlerClickMonitor(item)">
          <img :src="item.img" alt="" :class="['img-item', index > 0 ? 'm-l-10' : '']" />
        </div>
      </div>
      <div class="right-wrap m-r-10" v-if="type === 'area'">
        <n-space vertical>
          <n-select v-model:value="chooseArea" :options="areaList" class="area-list"
            :on-update:value="handleAreaChange" />
        </n-space>
      </div>
      <div class="right-wrap m-r-10" v-if="type === 'orgs'">
        <n-space vertical>
          <n-select v-model:value="chooseOrgs" :options="orgsList" class="area-list"
            :on-update:value="handleOrgsChange" />
        </n-space>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, toRefs, watch } from 'vue'
import icJiankongTop1 from '@/assets/images/chart/custom/ic_jiankong_top_1.png'
import icJiankongTop2 from '@/assets/images/chart/custom/ic_jiankong_top_2.png'
import icJiankongTop3 from '@/assets/images/chart/custom/ic_jiankong_top_3.png'
import icJiankongTop4 from '@/assets/images/chart/custom/ic_jiankong_top_4.png'
import icJiankongTop5 from '@/assets/images/chart/custom/ic_jiankong_top_5.png'
import icJiankongTop6 from '@/assets/images/chart/custom/ic_jiankong_top_6.png'
import icJiankongTop7 from '@/assets/images/chart/custom/ic_jiankong_top_7.png'
import { useRoute } from 'vue-router'
import { apiGetCanteenChannelList } from '@/api/path'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { debounce } from 'lodash'

const route = useRoute()
const emit = defineEmits(['selectHandle'])
const props = defineProps({
  timeType: {
    type: String,
    default: 'month'
  },
  show: {
    type: Boolean,
    default: true
  },
  isShowHeadTitle: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: ''
  },
  areaColor: {
    type: String,
    default: ''
  },
})

const selectDateType = (type: string) => {
  emit('selectHandle', type)
}
// 图标列表
const imgList = ref(
  [
    {
      name: '清屏',
      img: icJiankongTop1
    },
    {
      name: '保存为预设',
      img: icJiankongTop2
    },
    {
      name: '单分屏',
      img: icJiankongTop3
    },
    {
      name: '四分屏',
      img: icJiankongTop4
    },
    {
      name: '九分屏',
      img: icJiankongTop5
    },
    {
      name: '十六分屏',
      img: icJiankongTop6
    },
    {
      name: '全屏',
      img: icJiankongTop7
    }
  ]
)
// 选择的区域
const chooseArea = ref()
// 区域列表
const areaList = ref<Array<any>>()

// 选择组织
const chooseOrgs = ref()
// 组织列表
const orgsList = ref<Array<any>>()
//  保存的数据
const chartEditStore = useChartEditStore()
// 渠道id
const channelId = ref(Number(route.query.channel_id))
// 点击
const handlerClickMonitor = (item) => {
  emit('selectHandle', item)
}
const handleAreaChange = (item) => {
  emit('selectHandle', item)
}
// 组织选择返回
const handleOrgsChange = (item) => {
  console.log('组织选择返回', item)
  chooseOrgs.value = item
  emit('selectHandle', item)
}
// 初始化数据
const initData = () => {
  if (props.type === 'area') {
    getAreaList()
  }
  if (props.type === 'orgs') {
    getOrgsList()
  }
}
// 获取区域列表
const getAreaList = async () => {
  let params = {
    channel_id: channelId.value,
    role_id: Number(route.query.role_id)
  }
  let res = await apiGetCanteenChannelList(params)
  if (res && res.code === 0) {
    let data = res.data || []
    if (data && Array.isArray(data) && data.length > 0) {
      orgsList.value = data.map(item => {
        return {
          ...item,
          label: item.name,
          value: item.id
        }
      })
      emit('selectHandle', data[0])
    }
  }
}
const getOrgsList = async () => {
  let params = {
    channel_id: channelId.value,
    role_id: Number(route.query.role_id)
  }
  let res = await apiGetCanteenChannelList(params)
  if (res && res.code === 0) {
    let data = res.data || []
    if (data && Array.isArray(data) && data.length > 0) {
      orgsList.value = data.map(item => {
        return {
          ...item,
          label: item.name,
          value: item.id
        }
      })
      chooseOrgs.value = data[0].id
      emit('selectHandle', data[0].id)
    }
  }
}
// 边框颜色
const borderColor = ref(props.areaColor)

onMounted(() => {
  initData()
})

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    if (newData) {
      console.log('newData 我的天啊', newData)
      // 如果有值，并且selecctValue为空
      if (channelId.value !== newData.value && props.type === 'orgs') {
        channelId.value = newData.value
        console.log('newData 我的天啊111111111', newData.value, channelId.value)
        debouncedGetOrgsList()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

const debouncedGetOrgsList = debounce(getOrgsList, 300)

</script>

<style lang="scss" scoped>
.fund-head {
  width: 100%;
  height: 50px;
  position: relative;
  background-image: url('@/assets/images/chart/custom/fund_head_bg.png');
  background-repeat: no-repeat;

  display: flex;
  justify-content: space-between;

  .head-title {
    margin-left: 40px;
    line-height: 50px;
    font-weight: 700;
    color: #fff;
    font-size: 24px;
    font-style: italic;
    letter-spacing: 4px;
  }

  .right-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn-item {
      width: 100px;
      height: 38px;
      line-height: 38px;
      text-align: center;
      font-size: 18px;
      cursor: pointer;
    }

    .active-bg {
      background-image: url('@/assets/images/chart/custom/active_btn_bg.png');
    }

    .un-active-bg {
      background-image: url('@/assets/images/chart/custom/un_active_btn_bg.png');
    }

    .proportionOfRawMaterial-item {
      position: relative;
    }

    .proportionOfRawMaterial-item::before {
      content: '';
      width: 14px;
      height: 14px;
      background: #26BDFF;
      position: absolute;
      top: 4px;
      left: -16px;
      border-radius: 4px;
    }

    .contract::before {
      background: #FF9A35 !important;
    }

    .revenueExpenditure-item {
      position: relative;
    }

    .revenueExpenditure-item::before {
      content: '';
      width: 32px;
      height: 2px;
      background: #0BF9FE;
      position: absolute;
      top: 11px;
      left: -40px;
    }

    .yearProfit-item {
      position: relative;
    }

    .yearProfit-item::before {
      content: '';
      width: 32px;
      height: 2px;
      background: rgba(15, 119, 255, 1);
      position: absolute;
      top: 11px;
      left: -40px;
    }

    .yellow::before {
      background: #FF9A35 !important;
    }

    .yellow2::before {
      background: #ffc73a !important;
    }

    .area-list {
      width: 158px;
      height: 36px;
    }
  }

  .img-item {
    width: 24px;
    height: 24px;
    cursor: pointer;
  }

  .img-item:hover {
    opacity: 0.8;
  }

  @include deep() {
    .n-base-selection-label {
      display: flex;
      align-items: center;
    }

    .n-base-selection .n-base-selection-label {
      display: flex;
      align-items: center;
      border-radius: 2px !important;
      border: 1px solid v-bind('borderColor') !important;

      .n-base-selection-input__content {
        color: v-bind('borderColor') !important;
      }

      .n-base-suffix__arrow {
        color: v-bind('borderColor') !important;
      }
    }

    .n-base-select-menu .n-base-select-option.n-base-select-option--selected {
      color: v-bind('borderColor') !important;
    }
  }
}
</style>