import { PublicConfigClass } from '@/packages/public'
import { PieCostExpend } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import peropleImage from '@/assets/images/chart/custom/ic_green_people.png'
export const includes = ['legend', 'xAxis', 'yAxis', 'grid']
import { chartInitConfig } from '@/settings/designSetting'
import  res  from './data.json'
// 数据配置
export const option = {
  titleValue: '成本支出占比-本月',
  tableDataType: 'costExpenditure',
  dataList: res.week
}
// 导出配置
export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = PieCostExpend.key // 组件key
  public chartConfig = cloneDeep(PieCostExpend)
  public attr = { ...chartInitConfig, w: 596, h: 440, zIndex: -1 }
  // 图表配置项
  public option = cloneDeep(option)
}
