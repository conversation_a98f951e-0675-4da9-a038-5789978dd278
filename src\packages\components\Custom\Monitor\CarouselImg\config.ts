import { PublicConfigClass } from '@/packages/public'
import { CarouselImg } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import { chartInitConfig } from '@/settings/designSetting'
import dataJson from './data.json'

export const option = {
  titleValue: "学校资质", // 标题
  dataList: [] , // 数据列表
  type: '', // 类型
  dataType: 'schoolQualification', // 数据类型 
  typeList: cloneDeep(dataJson.typeList),
  selectValue: '' as string | number,  // 选择项
  orgId : 0  as string | number
}

export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = CarouselImg.key
  public chartConfig = cloneDeep(CarouselImg)
  public attr = {...chartInitConfig, w:519, h:250, zIndex:-1}
  // 图表配置项
  public option = cloneDeep(option)
}
