import dataForMonthImage from '@/assets/images/chart/custom/data_for_month.png'
import inboundPriceImage from '@/assets/images/chart/custom/inbound_price.png'
import outboundPriceImage from '@/assets/images/chart/custom/outbound_price.png'
import deviceImage from '@/assets/images/chart/custom/food_device.png'
import keepSampleImage from '@/assets/images/chart/custom/food_keep_sample.png'
import takeSampleImage from '@/assets/images/chart/custom/food_take_sample.png'

export const canteen_materials = {
  "weekData": {
    "shangyouerzhong": [
      {
        "title": '采购金额合计',
        "value": '16400.5',
        "img": dataForMonthImage
      },
      {
        "title": '入库成本合计',
        "value": '16400.5',
        "img": inboundPriceImage
      },
      {
        "title": '出库金额合计',
        "value": '12000',
        "img": outboundPriceImage
      }
    ],
    "shangyouyizhong": [
      {
        "title": '采购金额合计',
        "value": '13300.2',
        "img": dataForMonthImage
      },
      {
        "title": '入库成本合计',
        "value": '13300.2',
        "img": inboundPriceImage
      },
      {
        "title": '出库金额合计',
        "value": '10000',
        "img": outboundPriceImage
      }
    ],
    "anyuan": [
      {
        "title": '采购金额合计',
        "value": '12700.8',
        "img": dataForMonthImage
      },
      {
        "title": '入库成本合计',
        "value": '12700.8',
        "img": inboundPriceImage
      },
      {
        "title": '出库金额合计',
        "value": '9000',
        "img": outboundPriceImage
      }
    ],
    "zhanggong": [
      {
        "title": '采购金额合计',
        "value": '19800',
        "img": dataForMonthImage
      },
      {
        "title": '入库成本合计',
        "value": '19800',
        "img": inboundPriceImage
      },
      {
        "title": '出库金额合计',
        "value": '16000',
        "img": outboundPriceImage
      }
    ],
    "dayu": [
      {
        "title": '采购金额合计',
        "value": '11500.4',
        "img": dataForMonthImage
      },
      {
        "title": '入库成本合计',
        "value": '11500.4',
        "img": inboundPriceImage
      },
      {
        "title": '出库金额合计',
        "value": '8500',
        "img": outboundPriceImage
      }
    ]
  },
  "monthData": {
    "shangyouerzhong": [
      {
        "title": '采购金额合计',
        "value": '86400.5',
        "img": dataForMonthImage
      },
      {
        "title": '入库成本合计',
        "value": '86400.5',
        "img": inboundPriceImage
      },
      {
        "title": '出库金额合计',
        "value": '70000',
        "img": outboundPriceImage
      }
    ],
    "shangyouyizhong": [
      {
        "title": '采购金额合计',
        "value": '72300.2',
        "img": dataForMonthImage
      },
      {
        "title": '入库成本合计',
        "value": '72300.2',
        "img": inboundPriceImage
      },
      {
        "title": '出库金额合计',
        "value": '60000',
        "img": outboundPriceImage
      }
    ],
    "anyuan": [
      {
        "title": '采购金额合计',
        "value": '68700.8',
        "img": dataForMonthImage
      },
      {
        "title": '入库成本合计',
        "value": '68700.8',
        "img": inboundPriceImage
      },
      {
        "title": '出库金额合计',
        "value": '58000',
        "img": outboundPriceImage
      }
    ],
    "zhanggong": [
      {
        "title": '采购金额合计',
        "value": '102500',
        "img": dataForMonthImage
      },
      {
        "title": '入库成本合计',
        "value": '102500',
        "img": inboundPriceImage
      },
      {
        "title": '出库金额合计',
        "value": '85000',
        "img": outboundPriceImage
      }
    ],
    "dayu": [
      {
        "title": '采购金额合计',
        "value": '63800.4',
        "img": dataForMonthImage
      },
      {
        "title": '入库成本合计',
        "value": '63800.4',
        "img": inboundPriceImage
      },
      {
        "title": '出库金额合计',
        "value": '52000',
        "img": outboundPriceImage
      }
    ]
  }
}

export const school_information = {
    "shangyouerzhong": [
      {
        title: '学校类型',
        value: '中学',
      },
      {
        title: '经营类型',
        value: '自营食堂',
      },
      {
        title: '在校就餐人数',
        value: '800',
      }
    ],
    "shangyouyizhong": [
      {
        title: '学校类型',
        value: '中学',
      },
      {
        title: '经营类型',
        value: '托管/承包食堂',
      },
      {
        title: '在校就餐人数',
        value: '750',
      }
    ],
    "anyuan": [
      {
        title: '学校类型',
        value: '中学',
      },
      {
        title: '经营类型',
        value: '自营食堂',
      },
      {
        title: '在校就餐人数',
        value: '700',
      }
    ],
    "zhanggong": [
      {
        title: '学校类型',
        value: '小学',
      },
      {
        title: '经营类型',
        value: '托管/承包食堂',
      },
      {
        title: '在校就餐人数',
        value: '1000',
      }
    ],
    "dayu": [
      {
        title: '学校类型',
        value: '小学',
      },
      {
        title: '经营类型',
        value: '自营食堂',
      },
      {
        title: '在校就餐人数',
        value: '650',
      }
    ]
}

export const material_samples = {
  "shangyouerzhong": [
    {
      "title": '设备在线',
      "value": '3/3',
      "img": deviceImage
    },
    {
      "title": '当日留样品数',
      "value": '48',
      "img": keepSampleImage
    },
    {
      "title": '当日取样品数',
      "value": '12',
      "img": takeSampleImage
    }
  ],
  "shangyouyizhong": [
    {
      "title": '设备在线',
      "value": '3/3',
      "img": deviceImage
    },
    {
      "title": '当日留样品数',
      "value": '52',
      "img": keepSampleImage
    },
    {
      "title": '当日取样品数',
      "value": '14',
      "img": takeSampleImage
    }
  ],
  "anyuan": [
    {
      "title": '设备在线',
      "value": '3/3',
      "img": deviceImage
    },
    {
      "title": '当日留样品数',
      "value": '40',
      "img": keepSampleImage
    },
    {
      "title": '当日取样品数',
      "value": '10',
      "img": takeSampleImage
    }
  ],
  "zhanggong": [
    {
      "title": '设备在线',
      "value": '3/3',
      "img": deviceImage
    },
    {
      "title": '当日留样品数',
      "value": '56',
      "img": keepSampleImage
    },
    {
      "title": '当日取样品数',
      "value": '16',
      "img": takeSampleImage
    }
  ],
  "dayu": [
    {
      "title": '设备在线',
      "value": '3/3',
      "img": deviceImage
    },
    {
      "title": '当日留样品数',
      "value": '36',
      "img": keepSampleImage
    },
    {
      "title": '当日取样品数',
      "value": '8',
      "img": takeSampleImage
    }
  ]
}