// 财务大屏 --------------------- start
// 收入分支数据
export const FINANCE_LABEL_LIST = [
    {
        "name": "预充值收入",
        "value": 0,
        "class": "green",
        "key": "recharge"
    },
    {
        "name": "第三方消费收入",
        "value": 0,
        "class": "yellow",
        "key": "third_consume"
    },
    {
        "name": "伙食缴费收入",
        "value": 0,
        "class": "red",
        "key": "huoshifei"
    },
    {
        "name": "财政补贴",
        "value": 0,
        "class": "blue",
        "key": "government_subsidy"
    },
    {
        "name": "公益捐赠",
        "value": 0,
        "class": "green-light",
        "key": "public_welfare_donation"
    },
    // {
    //   name: '采购退款',
    //   value: 0,
    //   class: 'purple',
    //   key: 'purchase_refund'
    // },
    {
        "name": "非营业性-其他",
        "value": 0,
        "class": "grey",
        "key": "non_operating_other_income"
    },
    {
        "name": "营业性-其他",
        "value": 0,
        "class": "grey-light",
        "key": "operating_other_income"
    }
]
// chartOptionData
export const FINANCE_OPTION_DATA = [
    { value: 0, name: '预充值收入', data_type: 'recharge', itemStyle: { color:'#17E9AD' } },
    { value: 0, name: '第三方消费收入', data_type: 'third_consume',itemStyle: { color:'#FFD364' }  },
    { value: 0, name: '伙食缴费收入', data_type: 'huoshifei', itemStyle: { color:'#EF7647' } },
    { value: 0, name: '财政补贴', data_type: 'government_subsidy', itemStyle: { color:'#0296D9' } },
    { value: 0, name: '公益捐赠', data_type: 'public_welfare_donation', itemStyle: { color:'#93EF04' } },
    { value: 0, name: '采购退款', data_type: 'non_operating_other_costs', itemStyle: { color:'#4B04E4' } },
    { value: 0, name: '非营业性-其他', data_type: 'non_operating_other_income', itemStyle: { color:'#727C79' } },
    { value: 0, name: '营业性-其他', data_type: 'operating_other_income', itemStyle: { color:'#5E87B5' } }
]
// 财务大屏 --------------------- end

// 膳食大屏 --------------------- start
// 收入分支数据
export const DIET_LABEL_LIST = [
    {
      name: '储值消费',
      value: 0,
      class: 'skyBlue',
      key: 'cz_price'
    },
    {
      name: '补贴消费',
      value: 0,
      class: 'purple',
      key: 'bt_price'
    },
    {
      name: '第三方消费',
      value: 0,
      class: 'brightcyan',
      key: 'ds_price'
    },
    {
      name: '财政补贴',
      value: 0,
      class: 'yellow',
      key: 'bz_price'
    },
    {
      name: '公益捐赠',
      value: 0,
      class: 'darkblue',
      key: 'gy_price'
    },
    {
      name: '非营业性-其他',
      value: 0,
      class: 'grey',
      key: 'nqt_price'
    },
    {
      name: '营业性-其他',
      value: 0,
      class: 'green',
      key: 'qt_price'
    }
]
// chartOptionData
export const DIET_OPTION_DATA = [
    { value: 0, name: '储值消费', data_type: 'cz_price', itemStyle: { color:'#38B2FF' } },
    { value: 0, name: '补贴消费', data_type: 'bt_price',itemStyle: { color:'#6456FF' }  },
    { value: 0, name: '第三方消费', data_type: 'ds_price', itemStyle: { color:'#0ADDE8' } },
    { value: 0, name: '财政补贴', data_type: 'bz_price', itemStyle: { color:'#FFD364' } },
    { value: 0, name: '公益捐赠', data_type: 'gy_price', itemStyle: { color:'#23588D' } },
    { value: 0, name: '非营业性-其他', data_type: 'nqt_price', itemStyle: { color:'#5E87B5' } },
    { value: 0, name: '营业性-其他', data_type: 'qt_price', itemStyle: { color:'#17E9AD' } }
]
// 膳食大屏 --------------------- end