<template>
  <div :style="`width:${w}px;height:${h}px;`" class="monitor-video m-t-10">
    <fund-head :title="option.value.titleValue" :style="`width:${w}px`" 
      @selectHandle="handlerClickMonitor">
    </fund-head>
    <div class="monitor-list" :style="`width:${w}px; height:${contentHeight};`" v-loading="dataLoading">
      <div v-for="(item, index) in videoDemoList" :key="index"
        :class="{ 'live-right-active': index === liveType }" class="live-right">
        <div class="plugin">
          <div v-if="item.url" class="video-container">
            <!-- HLS 播放器 -->
            <!-- <video 
              ref="hlsVideoRef" 
              :id="`hls-${item.id}`" 
              class="video-box" 
              style="object-fit:fill" 
              autoplay 
              muted
              controls
              preload="auto"
              :loop="true"
              >
            </video> -->

            <video 
              ref="hlsVideoRef" 
              :id="`hls-${item.id}`" 
              class="video-box" 
              style="object-fit:fill" 
              autoplay 
              muted
              controls
              preload="auto"
              :loop="true"
              >
              <source :src="item.url" type="application/x-mpegURL">
            </video>
            
            <div class="video-bottom" v-if="false">
              <div class="video-title">{{ item.name }}</div>
              <div class="video-control">
                <div class="control-img" v-for="(controlItem, controlIndex) in imgList" :key="controlIndex" :name="controlItem.name"
                  @click="handlerClickMonitor(controlItem)">
                  <img :src="controlItem.img" alt="" :class="['img-item', controlIndex > 0 ? 'm-l-10' : '']" />
                </div>
              </div>
            </div>
          </div>
          <div v-else class="camera-flex error">
            <img :src="NoSign" alt="" class="no-sign-img" />
            <div class="no-sign">无信号</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, watchEffect, onMounted, reactive, onUnmounted } from 'vue'
import config from './config'
import { useChartDataFetch } from '@/hooks'
import { CreateComponentType } from '@/packages/index.d'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import cloneDeep from 'lodash/cloneDeep'
import { controlGlobalConfigType } from '@/store/modules/chartEditStore/chartEditStore.d'
import { MonitorVideo } from './index'
import { useControlUpdate } from '@/hooks/useControlUpdate.hook'
import { FundHead } from '@/components/FundHead'
import dData from './data.json'
import { CameraBox } from '@/components/CameraBox'
import { apiBackgroundFundSupervisionBigShieldChannelsPlayPost } from '@/api/path'
import NoSign from '@/assets/images/chart/custom/no_sign.png'
import icJiankongBottom1 from '@/assets/images/chart/custom/ic_jiankong_bottom_1.png'
import icJiankongBottom2 from '@/assets/images/chart/custom/ic_jiankong_bottom_2.png'
import icJiankongBottom3 from '@/assets/images/chart/custom/ic_jiankong_bottom_3.png'
import icJiankongBottom4 from '@/assets/images/chart/custom/ic_jiankong_bottom_4.png'
import icJiankongBottom5 from '@/assets/images/chart/custom/ic_jiankong_bottom_5.png'
import icJiankongBottom6 from '@/assets/images/chart/custom/ic_jiankong_bottom_6.png'
import icJiankongBottom7 from '@/assets/images/chart/custom/ic_jiankong_bottom_7.png'
import icJiankongBottom7 from '@/assets/images/chart/custom/video1.mp3'
import { useRoute } from 'vue-router'

const route = useRoute()

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})

// 长宽
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);

// 控件的key
const componentKey = MonitorVideo.key
const liveType = ref(0)
const showVideoNum = ref(4) // 显示的个数
const videoNetList = ref<Array<any>>([])
const videoList = ref<Array<any>>()
const videoDemoList = ref<Array<any>>([
  {
    id: 1,
    name: '视频1',
    url: 'https://naive-ui.oss-cn-beijing.aliyuncs.com/carousel-img/carousel1.jpeg'
  }
])
// 数据加载中
const dataLoading = ref(false)

// HLS播放器实例
const hlsPlayers = ref<Map<string, any>>(new Map())

// 图标列表
const imgList = ref(
  [
    {
      name: '性能面板',
      img: icJiankongBottom1
    },
    {
      name: '缩放',
      img: icJiankongBottom2
    },
    {
      name: '声音',
      img: icJiankongBottom3
    },
    {
      name: '暂停开始',
      img: icJiankongBottom4
    },
    {
      name: '截图',
      img: icJiankongBottom5
    },
    {
      name: '录制',
      img: icJiankongBottom6
    },
    {
      name: '全屏',
      img: icJiankongBottom7
    }
  ]
)
//  保存的数据
const chartEditStore = useChartEditStore()
// 心跳检测的定时器
let heartbeatInterval: any = null

// 配置
const option = reactive({
  value: cloneDeep(props.chartConfig.option)
})
console.log("option", option);

// 点击
const handlerClickMonitor = (item: any) => {
  console.log("handlerClickMonitor", item);
}

// 获取HLS播放地址
const getHLSUrl = (address: any) => {
  return address.hls || ''
}

// 初始化HLS播放器
const initHLSPlayer = async (videoElement: HTMLVideoElement, url: string, id: string) => {
  try {
    // 动态导入HLS.js
    const Hls = await import('hls.js')
    if (Hls.default.isSupported()) {
      const hls = new Hls.default({
        debug: false,
        enableWorker: true,
        lowLatencyMode: true,
        backBufferLength: 90,
        liveDurationInfinity: true,
        liveBackBufferLength: 0,
        liveSyncDuration: 3,
        xhrSetup: (xhr) => {
          // 尝试使用 credentials
          xhr.withCredentials = true;
        }
      })
      
      hls.loadSource(url)
      hls.attachMedia(videoElement)
      hlsPlayers.value.set(id, hls)
      
      hls.on(Hls.default.Events.MANIFEST_PARSED, () => {
        videoElement.play()
          .catch(error => {
            console.error('播放失败:', error)
          })
      })
      
      hls.on(Hls.default.Events.ERROR, (event, data) => {
        console.error('HLS Error:', data)
        if (data.fatal) {
          switch (data.type) {
            case Hls.default.ErrorTypes.NETWORK_ERROR:
              console.log('尝试恢复网络错误...')
              hls.startLoad()
              break
            case Hls.default.ErrorTypes.MEDIA_ERROR:
              console.log('尝试恢复媒体错误...')
              hls.recoverMediaError()
              break
            default:
              destroyPlayer(id)
              initHLSPlayer(videoElement, url, id)
              break
          }
        }
      })
    } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
      videoElement.src = url
      videoElement.addEventListener('loadedmetadata', () => {
        videoElement.play()
          .catch(error => {
            console.error('播放失败:', error)
          })
      })
    }
  } catch (error) {
    console.error('Failed to load HLS.js:', error)
  }
}

// 初始化视频播放器
const initVideoPlayer = async (item: any) => {
  await nextTick()
  
  const videoElement = document.getElementById(`hls-${item.id}`) as HTMLVideoElement
  if (!videoElement) return
  
  const url = item.url
  if (url) {
    await initHLSPlayer(videoElement, url, item.id)
    setupVideoListeners(videoElement)
  }
}

// 销毁播放器
const destroyPlayer = (id: string) => {
  const hlsPlayer = hlsPlayers.value.get(id)
  if (hlsPlayer) {
    hlsPlayer.destroy()
    hlsPlayers.value.delete(id)
  }
}

const getVideoList = async () => {
  dataLoading.value = true 
  const res = await apiBackgroundFundSupervisionBigShieldChannelsPlayPost({
    channel_id: option.value.selectValue ? option.value.selectValue : Number(route.query.channel_id),
    device_ids: chartEditStore.projectInfo.deviceId 
  })
  dataLoading.value = false
  
  if(res && res.code === 0) {
    console.log("apiBackgroundFundSupervisionBigShieldChannelsPlayPost", res)
    let data = res.data || []
    
    // 过滤出有效的视频数据（有address且没有错误信息）
    const validData = data.filter(item => item.address && !item.reason && !item.msg)
    videoNetList.value = cloneDeep(validData)
    
    // 确保视频列表初始化
    if (!videoList.value) {
      videoList.value = []
    }
    
    // 计算目标数量：最少4个，最多16个，但不超过有效数据数量
    // 确保数量是偶数，以便每行显示两个
    let targetCount = Math.min(Math.max(4, validData.length), 16)
    if (targetCount % 2 !== 0) {
      targetCount += 1 // 确保是偶数
    }
    
    // 清理所有现有播放器，准备重新创建
    if (videoList.value && videoList.value.length > 0) {
      videoList.value.forEach(item => {
        if (item.url) {
          destroyPlayer(`hls-${item.id}`)
        }
      })
    }
    
    // 重新创建视频列表，确保数量正确
    videoList.value = Array(targetCount).fill(0).map((_, i) => ({
      id: i,
      name: `视频${i+1}`,
      url: '',
      address: {}
    }))
    
    // 更新视频列表中的URL
    videoList.value.forEach((item, index) => {
      if (index < validData.length) {
        const tag = validData[index]
        const address = tag.address || {}
        
        // 获取HLS播放地址
        const url = getHLSUrl(address)
        
        item.url = url
        item.address = address
        item.name = tag.channel_id ? tag.channel_id.split('_').pop() : `视频${index+1}`
        
        // 初始化播放器
        if (url) {
          initVideoPlayer(item)
        }
      } else {
        // 超出有效数据范围的显示无信号
        item.url = ''
        item.address = {}
      }
    })
    
    console.log('videoList.value', videoList.value)
  } else {
    // 处理错误情况
    if (!videoList.value || videoList.value.length === 0) {
      // 如果没有视频列表，创建4个空的视频项
      videoList.value = Array(4).fill(0).map((_, i) => ({
        id: i,
        name: `视频${i+1}`,
        url: '',
        address: {}
      }))
    }
  }
}

watch(
  () => props.chartConfig.option,
  (newData: any) => {
    console.log("newData 哈哈哈哈", newData);
    // 组件数据变化
    if (newData && newData) {
      option.value = cloneDeep(newData)
    }
  },
  {
    immediate: true,
    deep: true
  }
)

watch(() => chartEditStore.getDeviceId, (newData: any) => {
  console.log("chartEditStore.getDeviceId", newData)
  if(newData){
    getVideoList()
  }
}, { deep: true })

watchEffect(() => {

})

onMounted(() => {
  if (!option.value.videoList || option.value.videoList.length === 0) {
    let showVideoList: any[] = []
    if (dData && dData.length >= showVideoNum.value) {
      showVideoList = dData.slice(0, showVideoNum.value)
    }
    videoList.value = cloneDeep(showVideoList)
  }
  console.log("onMounted", option.value);
  
  heartbeatInterval = setInterval(() => {
    videoList.value?.forEach(item => {
      const videoElement = document.getElementById(`hls-${item.id}`) as HTMLVideoElement
      if (videoElement && videoElement.paused && !videoElement.ended) {
        console.log('检测到视频已暂停，尝试重新播放')
        videoElement.play()
          .catch(e => console.error('心跳重播失败:', e))
      }
    })
  }, 10000) // 每10秒检查一次
})

onUnmounted(() => {
  // 清理所有HLS播放器
  hlsPlayers.value.forEach((player, id) => {
    player.destroy()
  })
  
  hlsPlayers.value.clear()
  
  // 清除心跳检测
  clearInterval(heartbeatInterval)
})

const setupVideoListeners = (videoElement) => {
  // 监听视频暂停事件
  videoElement.addEventListener('pause', () => {
    // 如果不是用户手动暂停，则重新播放
    if (!videoElement.seeking) {
      setTimeout(() => {
        videoElement.play()
          .catch(e => console.error('重新播放失败:', e))
      }, 1000)
    }
  })
  
  // 监听视频结束事件
  videoElement.addEventListener('ended', () => {
    videoElement.currentTime = 0
    videoElement.play()
      .catch(e => console.error('循环播放失败:', e))
  })
  
  // 监听错误事件
  videoElement.addEventListener('error', () => {
    console.error('视频播放错误，尝试重新加载')
    videoElement.load()
    setTimeout(() => {
      videoElement.play()
        .catch(e => console.error('重新加载失败:', e))
    }, 2000)
  })
}

// 在script部分添加计算属性
const contentHeight = computed(() => {
  // 使用固定高度，不根据视频数量增加容器高度
  // 从总高度中减去标题栏高度（42px）
  return `${h.value - 42}px`;
});

</script>

<style scoped lang="scss">
.monitor-video {
  .monitor-list {
    border-left: 1px solid #082F50;
    border-right: 1px solid #082F50;
    border-bottom: 1px solid #082F50;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-content: flex-start;
    padding: 16px;
    box-sizing: border-box;
    
    /* Always enable scrollbar when content overflows */
    overflow-y: auto;
    
    /* Custom scrollbar */
    &::-webkit-scrollbar {
      width: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(8, 47, 80, 0.5);
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(11, 249, 254, 0.5);
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: rgba(11, 249, 254, 0.8);
    }
  }

  .live-right {
    width: calc(50% - 16px);
    height: 196px;
    padding: 1px 1px;
    box-sizing: border-box;
    margin-bottom: 16px;
    flex: 0 0 calc(50% - 16px);
  }

  .camera-box {
    width: 100%;
    height: 100%;
  }

  .fixedbox {
    position: fixed;
    top: 30px;
    right: 60px;
  }

  .camera-iframe {
    margin: 0;
    padding: 0;
    border: 0;
  }

  .plugin {
    width: 100%;
    height: 100%;
    border: 1px solid #0BF9FE;
    background-color: #062A46;

    .video-container {
      position: relative;
      height: 100%;

      .video-bottom {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 10px;
        background: linear-gradient(90deg, rgba(38, 189, 255, 0.6) 0%, rgba(38, 189, 255, 0) 100%);
        color: #fff;
        font-size: 12px;
        z-index: 10;
        display: flex;
        justify-content: space-between;

        .video-control {
          display: flex;
          align-items: center;

          .img-item {
            width: 18px;
            cursor: pointer;
          }

          .img-item:hover {
            opacity: 0.8;
          }
        }
      }
    }

    .video-box {
      width: 100%;
      height: 100%;
      z-index: 9;
    }
  }

  .camera-flex {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 12px;

  }

  .no-sign-img {
    width: 80px;
    height: 80px;
  }

  .no-sign {
    color: #fff;
  }
}
</style>