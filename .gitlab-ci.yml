image: registry.cn-shenzhen.aliyuncs.com/packer-devops/docker:robot

stages:
- build_deploy


variables:
  REGISTRY_URL: "registry-vpc.cn-shenzhen.aliyuncs.com"
  TITLE: logistics-system
  IMAGE: ${TITLE}/image_catering/web
  DEBUG_HOST: **************
  SRY01_HOST: **************
  SRY02_HOST: **************
  PROD_3_0_S5: **************
  PROD_3_0_S6: **************

before_script:
#- docker login --username=$HUB_USER --password=$HUB_PASSWORD $HUB_DOMAIN
- COMMIT_SHA=${CI_COMMIT_SHA:0:8} # git.commit的提交代号
- IMAGE_VERSION=${CI_COMMIT_TAG:13} # 版本号,长度为打tag的前缀 len("open-") = 5
- TAR_TAG=image_catering_web_v${IMAGE_VERSION}.tar # 压缩包名称
- IMAGE_TAG=${IMAGE}:image_catering_web_v${IMAGE_VERSION} # 镜像包名称
- ALI_IMAGE_TAG=${HUB_DOMAIN}/packer/cashier_${TITLE}_web:v${IMAGE_VERSION} # 存储在阿里云镜像包名称
- LATEST_IMAGE_TAG=${IMAGE}:latest # 镜像包名称
- LATEST_TAR_TAG=image_catering_web_latest.tar


build_dev:
  stage: build_deploy
  script:
    - echo "local23开始打包静态资源"
    - ssh root@************* "cd /app/devops/$CI_PROJECT_NAME &&
      rm -rf dist &&
      rm -rf dist.tar.gz &&
      git checkout . &&
      git checkout dev &&
      git pull &&
      npm install --ignore-engines &&
      npm run build &&
      tar czf dist.tar.gz dist &&
      cd /root/app/frontend/$CI_PROJECT_NAME &&
      mkdir -p dist temp &&
      tar xzf /app/devops/$CI_PROJECT_NAME/dist.tar.gz -C temp &&
      rm -rf dist &&
      mv temp/dist . &&
      rm -rf temp &&
      cd /home/<USER>/.pushi/ && docker-compose restart nginx &&
      rm -rf /app/devops/$CI_PROJECT_NAME/dist.tar.gz"
      - echo "部署开发环境完成..."
  tags:
     - local-dev
  only:
    - dev
    
    
build_demo:
  stage: build_deploy
  script:
    - echo "local23开始打包静态资源"
    - ssh root@************* "cd /app/devops/$CI_PROJECT_NAME &&
      rm -rf dist &&
      git checkout . &&
      git checkout demo &&
      git pull &&
      export NODE_OPTIONS=--max_old_space_size=8192 &&
      npm install --ignore-engines &&
      npm run build "
    - ssh root@************* "cd /home/<USER>/$CI_PROJECT_NAME/ &&
      rm -rf dist &&
      scp -r root@*************:/app/devops/$CI_PROJECT_NAME/dist . &&
      cd /home/<USER>/.pushi/ && docker-compose restart nginx "
      #docker restart $(docker ps |grep nginx |awk '{print $1}')"
    - echo "部署开发环境完成..."
  tags:
     - local-dev
  only:
    - demo

