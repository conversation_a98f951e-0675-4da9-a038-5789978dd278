import { ConfigType, PackagesCategoryEnum, ChartFrameEnum } from '@/packages/index.d'
import { CustomCategoryEnum, CustomCategoryEnumName } from '../../index.d'

export const ConfigInfoDataCards: ConfigType = {
  key: 'ConfigInfoDataCards',
  chartKey: 'VConfigInfoDataCards',
  conKey: 'VCConfigInfoDataCards',
  title: '通用信息卡片',
  category: CustomCategoryEnum.Info,
  categoryName: CustomCategoryEnumName.Info,
  package: PackagesCategoryEnum.CUSTOM,
  chartFrame: ChartFrameEnum.COMMON,
  image: 'configurable_data_cards.png'
}
