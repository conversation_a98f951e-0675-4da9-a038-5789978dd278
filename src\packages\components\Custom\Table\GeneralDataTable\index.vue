<template>
  <div :style="`width:${w}px;height:${h}px;`" class="general-data-table c-white">
    <!--标题-->
    <table-head :title="option.value.titleValue" :dateTypeOptions="currentTableConfig.dateTypeOptions"
      :date-type="option.dateType" :style="`width:${w}px`" @select-handle="selectHandle">
    </table-head>
    <!--内容-->
    <div class="table-wrap" :style="`height:${h - 75}px;`">
      <div v-if="currentTableConfig.totalConfig" class="table-total ps-flex col-center m-b-10">
        <img src="@/assets/images/chart/custom/total_consumption.png" alt="" srcset="">
        <div class="ps-flex col-center f-s-24 m-l-20">
          <div>{{ currentTableConfig.totalConfig.title }}：</div>
          <div class="total-price">￥{{ option.totalPrice }}</div>
        </div>
      </div>
      <div class="table-header">
        <div class="table-header-top"></div>
        <div class="table-header-bottom"></div>
        <div
          class="ps-flex row-between">
          <!-- 动态生成表头 -->
          <div
            v-for="column in currentTableConfig.tableSetting"
            :key="column.key"
            class="item-title"
            :class="getColumnClass(column)"
          >
            {{ column.label }}
          </div>
        </div>
      </div>

      <div class="scroll-wrap"
        :style="`height:${currentTableConfig.scrollHeight ? currentTableConfig.scrollHeight : 305}px;overflow: hidden;`">
        <vue3-seamless-scroll class="scroll" v-model='option.value.isScroll' :list="totalData.value" :step="0.5"
          :hover="true" :limit-scroll-num="currentTableConfig.limiScrollNum" :wheel="true">
          <div class='tag-item' v-for="(item, index) in totalData.value" :key="index">
            <div class="ps-flex flex-align-center p-l-5 p-r-5">
              <!-- 动态生成表格内容 -->
              <div
                v-for="column in currentTableConfig.tableSetting"
                :key="column.key"
                class="item-info"
                :class="getColumnClass(column)"
              >
                <div v-if="column.type === 'index'">{{ index + 1}}</div>
                <div v-if="column.type === 'text'" class="text">{{ item[column.key] }}</div>
                <div v-if="column.type === 'price'" class="price">￥{{ formatDivideAmount(item[column.key]) || '0.00'}}</div>
                <!-- 有什么特殊类型，自己再写吧 -->
              </div>
            </div>
          </div>
        </vue3-seamless-scroll>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes, TableColumn, TableConfig } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { formatDivideAmount } from '@/utils'
import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'
import { TableHead } from '@/components/TableHead'
import { useRoute } from 'vue-router'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import dataJson from './data.json'
import {
  apiBackgroundFundSupervisionBigShieldDietRepastConsumeAmount,
  apiBackgroundFundSupervisionBigShieldIncome,
  apiGetIncomeProfitRank,
  apiBackgroundFundSupervisionBigShieldDietRawMaterialsPercentageRank,
  apiBackgroundFundSupervisionBigShieldDrpInventoryRecord,
  apiBackgroundFundSupervisionBigShieldDrpMaterialsData
} from '@/api/path'

// API 映射
const apiMap: Record<string, any> = {
  apiBackgroundFundSupervisionBigShieldDietRepastConsumeAmount,
  apiBackgroundFundSupervisionBigShieldIncome,
  apiGetIncomeProfitRank,
  apiBackgroundFundSupervisionBigShieldDietRawMaterialsPercentageRank,
  apiBackgroundFundSupervisionBigShieldDrpInventoryRecord,
  apiBackgroundFundSupervisionBigShieldDrpMaterialsData
}

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})

const route = useRoute()
const chartEditStore = useChartEditStore()

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);

// 配置
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option),
  totalPrice: 0, // 合计总金额
  dateType: 'month',
  channelId: Number(route.query.channel_id),
  orgId: Number(route.query.org_id),
  selectValue: 0 as string | number // 支持字符串和数字类型
})


const totalData = reactive({
  value: Array<any>()
})

// 当前表格设置
const currentTableConfig = computed(() => {
  // 从 optionData 中获取 tableConfig，如果没有则返回默认配置
  if (option.value.tableConfig) {
    return option.value.tableConfig as TableConfig
  }
  //  默认值
  return {
    label: '食堂物资数据',
    value: "CanteenMaterial",
    data: [],
    width: 673,
    height: 528,
    scrollHeight: 400,
    limiScrollNum: 10,
    dateType: 'week',
    dateTypeOptions: ['week', 'month'],
    totalConfig: {
      title: ''
    },
    apiConfig: {
      apiFunction: '',
      apiParams: []
    },
    tableSetting: []
  }
})

// 获取列的CSS类名
const getColumnClass = (column: any) => {
  // 优先使用配置中的 flex 值
  if (column.flex) {
    return `flex-${column.flex}`
  }
  return 'flex-1'
}

// 获取筛选后的数据
const getFilteredData = () => {
  if (option.value.tableDataType === 'CanteenMaterial') {
    const canteenData = dataJson.CanteenMaterialList

    // 根据 dateType 选择数据源
    let dataSource: any = {}
    if (option.dateType === 'week') {
      dataSource = canteenData.weekData
    } else if (option.dateType === 'month') {
      dataSource = canteenData.monthData
    } else {
      console.warn(`不支持的 dateType: ${option.dateType}`)
      return []
    }

    // 根据 selectValue 筛选具体数据
    let dataKey
    if (option.selectValue) {
      dataKey = option.selectValue
    } else {
      // 如果没有指定 selectValue，使用第一个可用的键
      dataKey = Object.keys(dataSource)[0] || 'shangyouerzhong'
    }

    const result = dataSource[dataKey] || []
    console.log(`数据筛选 - dateType: ${option.dateType}, selectValue: ${option.selectValue}, dataKey: ${dataKey}, 结果数量: ${result.length}`)
    return result
  } else if (option.value.tableDataType === 'FoodCapital') {
    const foodCapitalData = dataJson.FoodCapitalList
    let dataKey
    if (option.selectValue) {
      dataKey = option.selectValue
    } else {
      // 如果没有指定 selectValue，使用第一个可用的键
      dataKey = Object.keys(foodCapitalData)[0] || 'shangyouerzhong'
    }

    const result = foodCapitalData[dataKey] || []
    return result
  }


  return []
}

// 获取数据
const getTableData = async (type?: any) => {
  let params: any = {}
  let apiConfig = currentTableConfig.value.apiConfig
  if (apiConfig.apiParams) {
    apiConfig.apiParams.forEach((paramKey: string) => {
      // 配置apiParams的时候，org_id、org_list、channel_id 三选一，指代的是左上角选择框，请注意。
      switch (paramKey) {
        case 'org_id':
          params.org_id = option.selectValue ? option.selectValue : option.orgId
          break
        case 'org_list':
          params.org_list = option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined
          break
        case 'channel_id':
          params.channel_id = option.selectValue ? option.selectValue : option.channelId
          break
        case 'date_type':
          params.date_type = type ? type : option.dateType
          break
        default:
          params[paramKey] = null
      }
    })
  }
  // let apiFunction = apiMap[apiConfig.apiFunction]
  const res = await apiMap[apiConfig.apiFunction](params)
  if (res.code === 0) {
    if (res && res.data) {
      if (currentTableConfig.value.value === 'CanteenMaterial') {
        totalData.value = Object.values(res.data)
        totalData.value = totalData.value.filter(item => !(item.total_entry_fee === 0 && item.total_exit_fee === 0 && item.total_purchase_fee === 0))
      } else {
        totalData.value = res.data
      }
      // 根据类型获取合计金额
      switch (option.value.tableDataType) {
        case 'CafeteriaDiningConsumptionAmount':
          option.totalPrice = 0
          totalData.value.map(item => {
            if (item.price) {
            option.totalPrice += Number(item.price)
            }
            return item
          })
          option.totalPrice = formatDivideAmount(Number(option.totalPrice.toFixed(2)))
        break;
      }
    }
  }
}

const selectHandle = (type: string) => {
  option.dateType = type

  if (option.value.tableDataType === 'CanteenMaterial' || option.value.tableDataType === 'FoodCapital') {
    // 使用本地数据筛选
    const filteredData = getFilteredData()
    totalData.value = filteredData
  } else {
    // 使用 API 获取数据
    if (option.selectValue) {
      getTableData(option.dateType)
    }
  }
}
// 初始化
const initData = () => {
  option.dateType = currentTableConfig.value.dateType || 'week'

  if (option.value.tableDataType === 'CanteenMaterial' || option.value.tableDataType === 'FoodCapital') {
    // 使用本地数据筛选
    const filteredData = getFilteredData()
    totalData.value = filteredData
  } else {
    // 使用 API 或静态数据
    if (option.selectValue) {
      getTableData(option.dateType)
    } else {
      totalData.value = option.value.dataset || []
    }
  }
}

// 页面加载
onMounted(() => {
  initData()
})

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)
watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseSelect'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

</script>

<style scoped lang="scss">
.general-data-table {

  .table-wrap {
    background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
    padding: 15px 20px 10px;
    overflow: hidden;
  }

  .table-total{
    height: 44px;
    img{
      height: 42px;
    }
    .total-price{
      font-size: 34px;
      color: #26BDFF;
      font-weight: 600;
      margin-top: 3px;
    }
  }

  .table-header {

    position: relative;
    height: 36px; /* 设置你的div高度 */
    overflow: hidden; /* 确保伪元素不会溢出div */
    font-size: 17px;
    margin-bottom: 5px;
    border: 1px solid #02A9FF66;
    background: #04253D;
    padding: 0 5px;
    box-sizing: border-box;
    .item-title {
      height: 36px;
      line-height: 36px;
      color: #26BDFF;
      text-align: center;
    }

    .table-header-top::before,
    .table-header-top::after,
    .table-header-bottom::before,
    .table-header-bottom::after {
      content: '';
      position: absolute;
      width: 6px;
      /* 短边框的宽度 */
      height: 6px;
      /* 短边框的高度 */
    }

    .table-header-top::before {
      top: 0;
      right: 0;
      border-top: 2px solid #06A5FF;
      border-right: 2px solid #06A5FF;
    }

    .table-header-top::after {
      top: 0;
      left: 0;
      border-top: 2px solid #06A5FF;
      border-left: 2px solid #06A5FF;
    }

    .table-header-bottom::before {
      bottom: 0;
      right: 0;
      border-bottom: 2px solid #06A5FF;
      border-right: 2px solid #06A5FF;
    }

    .table-header-bottom::after {
      bottom: 0;
      left: 0;
      border-bottom: 2px solid #06A5FF;
      border-left: 2px solid #06A5FF;
    }
  }

  .scroll-wrap {
    // overflow: hidden;
  }

  .scroll {
    /* 必需要设置合适的高,因为他的原理是往列表最后添加列表重复元素，所以这个组件高不要大于其内容原先的最大高度 */
    // height: 305px;
    // min-width: 450px;
    // overflow: hidden;
  }

  .tag-item {

    min-width: 479px;
    min-height: 40px;
    .item-info {
      height: 40px;
      line-height: 40px;
      color: #FFF;
      text-align: center;
      font-size: 16px;
      overflow: hidden;
      .text, .price{
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }

    .health-item-img {

      // height: 80px!important;
      // line-height: 80px!important;
      img {
        // margin-top: 12px;
      }
    }

    .item-img {
      width: 260px;
      height: 178px;
      padding: 5px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .tag-item:nth-child(2n-1) {
    background: #0E48724D;
  }

  .flex-6 {
    flex: 6;
  }
  .flex-5 {
    flex: 5;
  }
  .flex-4 {
    flex: 4;
  }

  .flex-3 {
    flex: 3;
  }

  .flex-2 {
    flex: 2;
  }

  .flex-1 {
    flex: 1;
  }

  .green-color {
    color: #0BF9FE !important;
  }

  .green2-color {
    color: #29DC7A !important;
  }

  .red-color {
    color: #FF4343 !important;
  }

  .blue-color {
    color: #26BDFF !important;
  }

  .yellow-color {
    color: #FF9A35 !important;
  }

  .f-s-24 {
    font-size: 24px;
  }

  .f-w-600 {
    font-weight: 600;
  }
}
</style>