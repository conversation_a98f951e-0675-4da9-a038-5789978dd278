import { ConfigType, PackagesCategoryEnum, ChartFrameEnum } from '@/packages/index.d'
import { CustomCategoryEnum, CustomCategoryEnumName } from '../../index.d'

export const ConfigurableDataCards: ConfigType = {
  key: 'ConfigurableDataCards',
  chartKey: 'VConfigurableDataCards',
  conKey: 'VCConfigurableDataCards',
  title: '可配置数据卡片',
  category: CustomCategoryEnum.Collect,
  categoryName: CustomCategoryEnumName.Collect,
  package: PackagesCategoryEnum.CUSTOM,
  chartFrame: ChartFrameEnum.COMMON,
  image: 'configurable_data_cards.png'
}
