<template>
  <div :style="`width:${w}px;height:${h}px;`" class="total-warning">
    <div class="warning-list" :style="`width:${w}px;height:${h}px;`"
      v-loading="dataLoading">
      <div v-for="(item, index) in dataList" :key="index" :class="['list-tag', index === 0 ? 'm-l-30' : '']">
        <div class="tag-name">{{ item.label }}</div>
        <div class="tag-index" :class="{ 'warning-value': item.value > 0 }">{{ item.value }}</div>
        <div class="ver-line" v-if="index < dataList.length - 1"></div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, watchEffect, onMounted, reactive } from 'vue'
import config from './config'
import { FundHead } from '@/components/FundHead'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { TotalWarning } from './index'
import cloneDeep from 'lodash/cloneDeep'
import { apiBackgroundFundSupervisionBigShieldWarningOverviewPost } from '@/api/path'
import { useRoute } from 'vue-router'
import { parseTime } from '@/utils/time'

const route = useRoute()

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
// 长宽
const { w, h } = toRefs(props.chartConfig.attr)
const contentHeight = ref((h.value - 42))
console.log("w", w, "h", h);
// 控件的key
const componentKey = TotalWarning.key
// 配置
const option = reactive({
  value: cloneDeep(props.chartConfig.option)
})
console.log("option", option);
// 数据列表
const dataList = ref<Array<any>>(option.value.dataList)
// 数据加载中
const dataLoading = ref(false)

//  保存的数据
const chartEditStore = useChartEditStore()
// 区域改变
const handlerClickArea = (item: any) => {
  console.log("item", item);
}
// 初始化数据
const initData = async () => {
  dataList.value = [
    {
      "value": 1,
      "key": "profit_warn_count",
      "label": "利润预警"
    },
    {
      "value": 0,
      "key": "materials_warn_count",
      "label": "原材料支出预警"
    },
    {
      "value": 3,
      "key": "materials_risk_warn_count",
      "label": "出入库预警"
    },
    {
      "value": 2,
      "key": "documents_overdue_warn_count",
      "label": "证件过期预警"
    },
    {
      "value": 0,
      "key": "contract_overdue_warn_count",
      "label": "合同过期预警"
    }
  ]
  // getDataList()
}
// 获取数据
const getDataList = async (type?: any) => {
  dataLoading.value = true
  let params = {
    channel_id: option.value.selectValue ? option.value.selectValue : Number(route.query.channel_id),
    date: parseTime(new Date(), '{y}-{m}'),
    role_id: Number(route.query.role_id)
  }
  if (route.query.role_id) {
      Reflect.set(params, 'role_id', Number(route.query.role_id))
    }
  let res = await apiBackgroundFundSupervisionBigShieldWarningOverviewPost(params)
  
  dataLoading.value = false
  if (res && res.code === 0) {
    console.log("apiBackgroundFundSupervisionBigShieldWarningOverviewPost", res, dataList.value)
    const data = res.data || {}
    let newList = cloneDeep(dataList.value)
    if (typeof data === 'object' && Object.keys(data).length > 0) {
      // 经营预警
      let profitWarnCount = (data.profit_warn_count || 0) + (data.materials_warn_count || 0)
      // 出入库预警
      let materialsRiskWarnCount = data.materials_risk_warn_count || 0
      // 证件合同预警
      let documentsOverdueWarnCount = (data.documents_overdue_warn_count || 0) + (data.contract_overdue_warn_count || 0)
      // 其他预警
      let contract_overdue_warn_count = data.other_warn_count || 0
      newList = [
        {
          label: '经营预警',
          value: profitWarnCount,
          key: 'materials_warn_count'
        },
        {
          label: '出入库预警',
          value: materialsRiskWarnCount,
          key: 'materials_risk_warn_count'
        },
        {
          label: '证件合同预警',
          value: documentsOverdueWarnCount,
          key: 'documents_overdue_warn_count'
        },
        {
          label: '其他预警',
          value: contract_overdue_warn_count,
          key: 'other_warn_count'
        }
      ]
    }
    console.log("newList", newList)
    dataList.value = cloneDeep(newList)
  } else {
    dataList.value = []
  }
}

onMounted(() => {
  initData()
})


watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.value.selectValue) {
        option.value.selectValue = newData.value
        initData()
      } else if (option.value.selectValue !== newData.value) {
        option.value.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

</script>
<style scoped lang="scss">
  .total-warning {
    border: 1px solid #082F50;
    .warning-list{
      display: flex;
      align-items: center;
      justify-content: space-around;
    }

    .list-tag {
      display: inline-flex;
      flex-direction: column;
      position: relative;
      padding: 0 40px 0 0;
      
      .tag-name {
        font-size: 16px;
        height: 16px;
        color: #fff;
      }
    }
    .tag-index {
      height: 42px;
      color: #0BF9FE;
      font-size: 36px;
      text-align: left;
    }
    .warning-value {
      color: #FF4D4F;
    }
    .ver-line{
      width: 1px;
      height: 52px;
      background: #003C60;
      position: absolute;
      right: 0;
    }
  }
</style>