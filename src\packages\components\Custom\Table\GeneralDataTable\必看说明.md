# FundDataTableNew 组件操作说明文档

## 📋 组件概述

FundDataTableNew 是一个简化的配置化数据表格组件，支持多种表格类型展示，具有滚动、时间筛选、动态配置等功能。

## 🎯 主要特性

- ✅ **多表格类型支持**：收入情况、收支利润排行等
- ✅ **配置化驱动**：通过 config.vue 统一管理表格配置
- ✅ **动态表头**：基于 tableSetting 配置动态生成表头和内容
- ✅ **时间筛选**：支持本月、本年时间维度切换
- ✅ **滚动展示**：支持无缝滚动和滚动限制配置
- ✅ **API 集成**：自动调用对应的后端接口获取数据
- ✅ **简化渲染**：直接在模板中处理不同数据类型的显示

## 🔧 配置界面操作

在右侧配置面板中可以进行以下设置：

#### 标题设置
- **位置**：基础配置 → 标题
- **操作**：直接输入文本，最多12个字符
- **效果**：实时更新组件标题

#### 表格类型选择
- **位置**：基础配置 → 选择类型
- **选项**：收入情况、收支利润排行
- **效果**：切换不同的表格结构和数据源


## 🛠️ 基础配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| titleValue | string | '收入情况' | 表格标题 |
| tableDataType | string | 'incomeSituation' | 表格类型 |
| isScroll | boolean | true | 是否启用滚动 |
| tableConfig | object | {} | 表格详细配置（由config.vue自动设置） |

### 1. 表格配置tableConfig详解

```typescript
interface TableConfig {
  label: string // 表格名称
  value: string // 表格唯一标识
  data: any[] // 静态数据
  height: number // 表格总高度
  width: number // 表格总宽度
  scrollHeight: number // 滚动高度
  limiScrollNum: number, // 最小滚动条数
  dateType: string, // 默认时间类型
  dateTypeOptions: string[] // 显示的时间类型选项，为空则不显示时间筛选
  apiConfig: any // API配置
  totalConfig: any // 合计配置
  tableSetting: any[] // 表头配置
}
```

### 2. tableSetting 配置结构

```typescript
interface TableColumn {
  label: string    // 列标题
  key: string      // 数据字段名
  type: string     // 数据类型：index|text|price|date
  flex: number     // 列宽比例
}
```

### 3. 列类型说明

| 类型 | 说明 | 示例 | 渲染逻辑 |
|------|------|------|----------|
| index | 序号列，自动生成行号 | 1, 2, 3... | `index + 1` |
| text | 文本列，直接显示字段值 | "第一食堂" | `item[column.key] \|\| '--'` |
| price | 价格列，自动添加货币符号 | "￥1,234.56" | `divide(item[column.key])` |
| date | 日期列，格式化日期显示 | "2024-01-01" | `item[column.key] \|\| '--'` |

### 4. API自动化调用

#### API配置结构

```typescript
interface ApiConfig {
  apiFunction: string    // API函数名
  apiParams: string[]    // 请求参数列表
}
```
配置apiParams的时候，org_id、org_list、channel_id 三选一，指代的是左上角选择框，请注意

#### API函数引入

```typescript
import {
  apiBackgroundFundSupervisionBigShieldIncome,
  apiGetIncomeProfitRank
} from '@/api/path'
```

#### API映射机制

```typescript
// 组件内部的API映射
const apiMap: Record<string, any> = {
  apiBackgroundFundSupervisionBigShieldIncome,
  apiGetIncomeProfitRank
}

// 根据配置自动调用对应API
const apiFunction = apiMap[currentTableConfig.apiConfig.apiFunction]
const response = await apiFunction(params)
```

## 🎨 样式自定义

### 1. 组件尺寸

```typescript
// 通过 chartConfig.attr 控制
{
  attr: {
    w: 596,  // 宽度
    h: 512   // 高度
  }
}
```

### 2. 滚动配置

```typescript
{
  isScroll: true,        // 启用滚动（由config.vue控制）
  // 滚动参数由表格配置自动设置
  scrollHeight: currentTableConfig.scrollHeight,
  limiScrollNum: currentTableConfig.limiScrollNum
}
```

## 🔄 时间筛选功能

### 1. 时间选择器配置

```typescript
// 在表格配置中设置支持的时间类型
dateTypeOptions: ['month', 'year']  // 当前支持的时间类型
```

### 2. 时间切换处理

```typescript
// 组件内部自动处理时间切换
watch(() => option.dateType, (newDateType) => {
  // 自动重新获取对应时间维度的数据
  initData()
}, { immediate: true })
```

## 📚 添加新表指南

### 1. 添加新的表格类型

在 config.vue 的 selectTableOptions 中添加：

```typescript
{
  label: '收入情况',
  value: 'incomeSituation',
  data: dataJson.incomeSituationList,
  width: 673,
  height: 875,
  scrollHeight: 747,
  limiScrollNum: 7,
  dateType: 'month',
  dateTypeOptions: ['month', 'year'],
  apiConfig: {
    apiFunction: 'apiBackgroundFundSupervisionBigShieldIncome',
    apiParams: ['org_list', 'date_type']
  },
  totalConfig: {
    title: '消费总金额'
  },
  tableSetting: [
    { label: '序号', key: 'no', type: 'index', flex: 1 },
    { label: '类型', key: 'flow_type', type: 'text', flex: 2 },
    { label: '项目', key: 'data_type', type: 'text', flex: 3 },
    { label: '金额', key: 'fee', type: 'price', flex: 3 }
  ]
}
```

### 2. 添加新的API函数

导入新的API函数：

```typescript
import { apiNewTableData } from '@/api/path'
```

在 apiMap 中添加新的API映射：

```typescript
const apiMap: Record<string, any> = {
  // 现有API...
  apiNewTableData: apiNewTableData  // 新增API
}
```

### 3. 在tableConfig中添加新的配置参数

在config.ts中添加新参数
在config.vue中配置新参数，在index.vue中添加默认返回值
切记切记切记不要影响旧表显示


## 🐛 常见问题

### 1. 数据不显示

**问题**：表格组件渲染但没有数据
**解决**：
- 检查 API 接口是否正常返回数据
- 确认 tableDataType 配置正确
- 查看浏览器控制台是否有错误信息

### 2. 表头显示异常

**问题**：表头列数与数据不匹配
**解决**：
- 检查 tableSetting 配置是否正确
- 确认数据字段名与 key 配置一致
- 验证 flex 比例设置合理

### 3. 滚动不生效

**问题**：数据较多但不滚动
**解决**：
- 确认 isScroll 设置为 true
- 检查 scrollHeight 是否合理
- 验证 limiScrollNum 配置