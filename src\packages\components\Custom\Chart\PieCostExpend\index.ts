import { ConfigType, PackagesCategoryEnum, ChartFrameEnum } from '@/packages/index.d'
import { CustomCategoryEnum, CustomCategoryEnumName } from '../../index.d'
export const PieCostExpend: ConfigType = {
  key: 'PieCostExpend',
  chartKey: 'VPieCostExpend',
  conKey: 'VCPieCostExpend',
  title: '成本支出占比-本月-3D', // 组件名称
  category: CustomCategoryEnum.Chart,
  categoryName: CustomCategoryEnumName.Chart,
  package: PackagesCategoryEnum.CUSTOM,
  chartFrame: ChartFrameEnum.COMMON,
  image: 'pie_cost_expend.png' // 组件封面图
}
