<template>
  <div :style="`width:${w}px;height:${h}px;`" class="configurable-data-cards c-white">
    <!--标题-->
    <table-head
      v-if="currentCardConfig.showTableHead"
      :title="option.value.titleValue"
      :dateTypeOptions="currentDateTypeOptions"
      :date-type="option.dateType"
      :style="`width:${w}px`"
      @select-handle="selectHandle">
    </table-head>
    
    <div class="content-data ps-flex row-between col-center">
      <div 
        v-for="(item, index) in totalData.value" 
        :key="index" 
        class="data-card-item"
        :class="getCardClass(index)"
        :style="getCardStyle(index)">
        
        <!-- 收支利润显示模式 -->
        <div v-if="currentDisplayMode === 'shouzhi'" class="shouzhi-card">
          <div class="title">{{ item.title }}</div>
          <div class="amount" :style="getColorStyle(index, 'amountColors', 'color')">{{ formatDivideAmount(item.value) }}</div>
          <div v-if="item.comparison" class="comparison">
            <span class="comparison-label">月环比</span>
            <span class="comparison-value">{{ item.comparison }}</span>
            <img v-if="getComparisonTrend(item.comparison) === 'up'"
                  src="@/assets/images/chart/custom/up_icon.png"
                  alt="上升" class="trend-icon">
            <img v-if="getComparisonTrend(item.comparison) === 'down'"
                  src="@/assets/images/chart/custom/down_icon.png"
                  alt="下降" class="trend-icon">
          </div>
        </div>

        <!-- 物资显示模式 -->
        <div v-if="currentDisplayMode === 'materials'" class="materials-card">
          <div class="img-container">
            <img v-if="item.img" :src="item.img" class="card-img">
          </div>
          <div class="title">{{ item.title }}</div>
          <!-- <div class="amount" :style="getColorStyle(index, 'amountColors', 'color')">￥{{ formatDivideAmount(item.value) }}</div> -->
          <div class="amount" :style="getColorStyle(index, 'amountColors', 'color')">￥{{ formatAmount(item.value) }}</div>
        </div>

        <!-- schoolInfo 学校信息情况 显示模式 -->
        <div v-if="currentDisplayMode === 'schoolInfo'" class="schoolInfo-card">
          <div class="title">{{ item.title }}</div>
          <div class="info" :style="getColorStyle(index, 'infoColors', 'color')">{{ item.value }}</div>
        </div>

        <!-- 食材留样 左图右字 显示模式 -->
        <div v-if="currentDisplayMode === 'samples'" class="samples-card">
          <div class="img-container">
            <img v-if="item.img" :src="item.img" class="card-img">
          </div>
          <div>
            <div class="title">{{ item.title }}</div>
            <div class="info" :style="getColorStyle(index, 'valueColors', 'color')">{{ item.value }}</div>
          </div>
        </div>
        
        <!-- 分隔线 -->
        <div v-if="shouldShowDivider(index)" 
             class="divider">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { divide, formatDivideAmount, formatAmount } from '@/utils'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import { TableHead } from '@/components/TableHead'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { useRoute } from 'vue-router'
import dataForMonthImage from '@/assets/images/chart/custom/data_for_month.png'
import inboundPriceImage from '@/assets/images/chart/custom/inbound_price.png'
import outboundPriceImage from '@/assets/images/chart/custom/outbound_price.png'
import deviceImage from '@/assets/images/chart/custom/food_device.png'
import keepSampleImage from '@/assets/images/chart/custom/food_keep_sample.png'
import takeSampleImage from '@/assets/images/chart/custom/food_take_sample.png'
import { canteen_materials, school_information, material_samples } from './data'
import {
  apiBackgroundFundSupervisionBigShieldFoodReservedSample,
  apiBackgroundFundSupervisionBigShieldDrpInventoryRecordCollect,
  apiBackgroundFundSupervisionBigShieldPublicNoticeData
} from '@/api/path'

// API 映射
const apiMap: Record<string, any> = {
  apiBackgroundFundSupervisionBigShieldFoodReservedSample,
  apiBackgroundFundSupervisionBigShieldDrpInventoryRecordCollect,
  apiBackgroundFundSupervisionBigShieldPublicNoticeData
}

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})

const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)
const route = useRoute()
const chartEditStore = useChartEditStore()

// 长宽获取
const { w, h } = toRefs(props.chartConfig.attr)

// 配置
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option),
  channelId: Number(route.query.channel_id),
  orgId: Number(route.query.org_id),
  selectValue: 0,
  dateType: ''
})

// 获取默认卡片配置
const getDefaultCardConfig = () => {
  return {
    label: '可配置数据卡片',
    value: "custom",
    data: [],
    width: 673,
    height: 200,
    displayMode: 'amount',
    showTableHead: true,
    dateType: '',
    dateTypeOptions: ['week'],
    iconColors: ['#0BF9FE'],
    amountColors: ['#0BF9FE'],
    showDivider: false,
    apiConfig: {
      apiFunction: '',
      apiParams: []
    }
  }
}

const totalData = reactive({
  value: Array<any>()
})
// 当前卡片配置
const currentCardConfig = computed(() => {
  const config = option.value.cardConfig || getDefaultCardConfig()
  // 确保所有必要属性都有默认值
  return {
    ...getDefaultCardConfig(),
    ...config
  }
})

// 当前API 配置
const apiConfig = computed(() => {
  const config = currentCardConfig.value as any
  return config.apiConfig || {
    apiFunction: '',
    apiParams: []
  }
})

// // 显示数据
const displayData = computed(() => {
  const data = option.value.dataset || []
  // 确保每个数据项都有必要的属性
  return data.map((item: any) => ({
    title: item.title || '',
    value: item.value || '',
    comparison: item.comparison || '',
    img: item.img || '',
    icon: item.icon || '',
    ...item
  }))
})

// 当前显示模式
const currentDisplayMode = computed(() => {
  const config = currentCardConfig.value as any
  return config.displayMode || 'amount'
})

// 当前时间选项
const currentDateTypeOptions = computed(() => {
  const config = currentCardConfig.value as any
  return config.dateTypeOptions || ['week']
})

// 样式计算方法
const getCardClass = (index: number) => {
  const config = currentCardConfig.value as any
  const mode = config.displayMode || 'amount'
  return `${mode}-mode card-${index}`
}

const getCardStyle = (_index: number) => {
  const itemCount = totalData.value.length
  return {
    width: `${Math.floor(100 / itemCount) - 2}%`
  }
}

const getColorStyle = (index: number, key: string, type: string = 'backgroundColor') => {
  const config = currentCardConfig.value as any
  const colors = config[key] || ['#0BF9FE', '#FFC73A', '#0BF9FE']
  return {
    [type]: colors[index] || colors[0],
  }
}

const shouldShowDivider = (index: number) => {
  const config = currentCardConfig.value as any
  return config.showDivider !== false && index < totalData.value.length - 1
}

const getComparisonTrend = (comparison: string) => {
  if (!comparison) return null
  const numValue = parseFloat(comparison.replace('%', ''))
  return numValue > 0 ? 'up' : numValue < 0 ? 'down' : null
}

// 获取数据
const getTableData = async (type?: any) => {
  let params: any = {}
  if (apiConfig.value.apiParams) {
    apiConfig.value.apiParams.forEach((paramKey: string) => {
      // 配置apiParams的时候，org_id、org_list、channel_id 三选一，指代的是左上角选择框，请注意。
      switch (paramKey) {
        case 'org_id':
          params.org_id = option.selectValue ? option.selectValue : option.orgId
          break
        case 'org_list':
          params.org_list = option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : undefined
          break
        case 'channel_id':
          params.channel_id = option.selectValue ? option.selectValue : option.channelId
          break
        case 'date_type':
          params.date_type = type ? type : option.dateType
          break
        default:
          params[paramKey] = null
      }
    })
  }
  const res = await apiMap[apiConfig.value.apiFunction](params)
  if (res.code === 0) {
    if (res && res.data) {
      if (option.value.cardDataType === 'material_samples') {
        totalData.value[0].value = res.data.online_device
        totalData.value[0].img = deviceImage
        totalData.value[1].value = res.data.reserved_count || 0
        totalData.value[1].img = keepSampleImage
        totalData.value[2].value = res.data.destroy_count || 0
        totalData.value[2].img = takeSampleImage
      } else if (option.value.cardDataType === 'canteen_materials') {
        totalData.value[0].value = res.data.total_purchase_fee || 0.00
        totalData.value[0].img = dataForMonthImage
        totalData.value[1].value = res.data.total_entry_fee || 0.00
        totalData.value[1].img = inboundPriceImage
        totalData.value[2].value = res.data.total_exit_fee || 0.00
        totalData.value[2].img = outboundPriceImage
      } else if (option.value.cardDataType === 'school_information') {
        totalData.value[0].value = res.data.school_type ? res.data.school_type : '--'
        totalData.value[1].value = res.data.canteen_type ? res.data.canteen_type : '--'
        totalData.value[2].value = res.data.order_count
      }
    }
  }
}

// 获取筛选后的数据
const getFilteredData = () => {
  if (option.value.cardDataType === 'canteen_materials') {
    const allData = canteen_materials

    // 根据 dateType 选择数据源
    let dataSource: any = {}
    if (option.dateType === 'week') {
      dataSource = allData.weekData
    } else if (option.dateType === 'month') {
      dataSource = allData.monthData
    } else {
      console.warn(`不支持的 dateType: ${option.dateType}`)
      return []
    }

    // 根据 selectValue 筛选具体数据
    let dataKey
    if (option.selectValue) {
      dataKey = option.selectValue
    } else {
      // 如果没有指定 selectValue，使用第一个可用的键
      dataKey = Object.keys(dataSource)[0] || 'shangyouerzhong'
    }

    const result = dataSource[dataKey] || []
    console.log(`数据筛选 - dateType: ${option.dateType}, selectValue: ${option.selectValue}, dataKey: ${dataKey}, 结果数量: ${result.length}`)
    return result
  } else if (option.value.cardDataType === 'school_information') {
    const allData = school_information
    let dataKey
    if (option.selectValue) {
      dataKey = option.selectValue
    } else {
      // 如果没有指定 selectValue，使用第一个可用的键
      dataKey = Object.keys(allData)[0] || 'shangyouerzhong'
    }

    const result = allData[dataKey] || []
    return result
  } else if (option.value.cardDataType === 'material_samples') {
    const allData = material_samples
    let dataKey
    if (option.selectValue) {
      dataKey = option.selectValue
    } else {
      // 如果没有指定 selectValue，使用第一个可用的键
      dataKey = Object.keys(allData)[0] || 'shangyouerzhong'
    }

    const result = allData[dataKey] || []
    return result
  }


  return []
}

// 事件处理
const selectHandle = (type: string) => {
 option.dateType = type

  if (option.value.cardDataType === 'canteen_materials' || option.value.cardDataType === 'school_information' || option.value.cardDataType === 'material_samples') {
    // 使用本地数据筛选
    const filteredData = getFilteredData()
    totalData.value = filteredData
  } else {
    // 使用 API 获取数据
    if (option.selectValue) {
      getTableData(option.dateType)
    }
  }
}

// 初始化
const initData = () => {
  option.dateType = currentCardConfig.value.dateType
  if (option.value.cardDataType === 'canteen_materials' || option.value.cardDataType === 'school_information' || option.value.cardDataType === 'material_samples') {
    // 使用本地数据筛选
    const filteredData = getFilteredData()
    totalData.value = filteredData
  } else {
    if (option.selectValue) {
      getTableData(option.dateType)
    } else {
      totalData.value = displayData.value
    }
  }

}

// 页面加载
onMounted(() => {
  initData()
})

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseSelect'),
  (newData: any) => {
    // 这里两个判断，第一，newData有没有值，selectValue 有没有值，第二，旧值selectValue 和新值 newData 有没有变化
    if (newData) {
      // 如果有值，并且selecctValue为空
      if (!option.selectValue) {
        option.selectValue = newData.value
        initData()
      } else if (option.selectValue !== newData.value) {
        option.selectValue = newData.value
        initData()
      }
    }
  },
  {
    immediate: false,
    deep: true
  }
)

</script>

<style scoped lang="scss">
.configurable-data-cards {
  background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
  border: 1px solid;
  border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7)) 1;
  box-sizing: border-box;

  .content-data {
    padding: 20px;
    gap: 20px;
  }

  .data-card-item {
    display: flex;
    align-items: center;
    position: relative;
  }

  // 收支显示模式
  .shouzhi-card{
    text-align: center;
    .title{
      font-size: 22px;
      font-weight: bold;
    }
    .amount{
      font-size: 36px;
      font-weight: bold;
      margin-bottom: 4px;
    }
  }

  // 金额显示模式样式
  .materials-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 100%;
    margin-top: 10px;

    .img-container {
      border-radius: 50%;
      .card-img {
        width: 100px;
        height: 84px;
      }
    }
    .title {
      font-size: 21px;
    }

    .amount {
      font-size: 32px;
      font-weight: bold;
      margin-bottom: 4px;
    }
  }

  .schoolInfo-card{
    overflow: hidden;
    .title {
      font-size: 16px;
    }

    .info {
      font-size: 30px;
      font-weight: bold;
      // 超出...显示
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  // 食材留样模式样式
  .samples-card {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 15px;

    .img-container {
      flex-shrink: 0;
        .card-img {
          width: 80px;
          height: 70px;
        }
    }

    .title {
      font-size: 12px;
    }

    .info {
      font-size: 30px;
      font-weight: bold;
    }
  }

  .divider {
    position: absolute;
    right: -10px;
    top: 10px;
    bottom: 10px;
    border: 1px solid #003C60;
  }
}
</style>
