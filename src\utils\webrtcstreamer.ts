// Extend RTCPeerConnection to include custom id property
declare global {
  interface RTCPeerConnection {
    id?: string;
  }
}

interface IceServerConfig {
  iceServers: any[];
}

interface WebRtcOptions {
  offerToReceiveAudio?: boolean;
  offerToReceiveVideo?: boolean;
}

class WebRtcStreamer {
  private videoElement: HTMLVideoElement | null;
  private srvurl: string;
  private pc: RTCPeerConnection | null = null;
  private mediaConstraints: WebRtcOptions = { offerToReceiveAudio: true, offerToReceiveVideo: true };
  private iceServers: IceServerConfig | null = null;
  private earlyCandidates: RTCIceCandidate[] = [];

  constructor(videoElement: string | HTMLVideoElement, srvurl: string) {
      if (typeof videoElement === 'string') {
          this.videoElement = document.getElementById(videoElement) as HTMLVideoElement;
      } else {
          this.videoElement = videoElement;
      }
      this.srvurl = srvurl;
  }

  private _handleHttpErrors(response: Response): Response {
      if (!response.ok) {
          throw new Error(response.statusText);
      }
      return response;
  }

  public connect(videourl: string, audiourl?: string, options?: string, localstream?: MediaStream, prefmime?: string): void {
      this.disconnect();

      if (!this.iceServers) {
          console.log('Get IceServers', this.srvurl);

          fetch(this.srvurl + '/api/getIceServers')
          // fetch(this.srvurl)
              .then(this._handleHttpErrors)
              .then(response => response.json())
              .then(response =>
                  this.onReceiveGetIceServers(response, videourl, audiourl, options, localstream, prefmime)
              )
              .catch(error => this.onError('getIceServers ' + error));
      } else {
          this.onReceiveGetIceServers(
              this.iceServers,
              videourl,
              audiourl,
              options,
              localstream,
              prefmime
          );
      }
  }

  public disconnect(): void {
      if (this.videoElement?.srcObject) {
          const stream = this.videoElement.srcObject as MediaStream;
          stream.getTracks().forEach(track => {
              track.stop();
          });
      }
      if (this.pc) {
          fetch(this.srvurl + '/api/hangup?peerid=' + this.pc.id)
              .then(this._handleHttpErrors)
              .catch(error => this.onError('hangup ' + error));

          try {
              this.pc.close();
          } catch (e) {
              console.log('Failure close peer connection:' + e);
          }
          this.pc = null;
      }
  }

  private onReceiveGetIceServers(
      iceServers: IceServerConfig,
      videourl: string,
      audiourl?: string,
      options?: string,
      stream?: MediaStream,
      prefmime?: string
  ): void {
      this.iceServers = iceServers;
      const pcConfig: any = iceServers || { iceServers: [] };
      try {
          this.createPeerConnection(pcConfig);

          let callurl = this.srvurl + '/api/call?peerid=' + this.pc?.id + '&url=' + encodeURIComponent(videourl);
          if (audiourl) {
              callurl += '&audiourl=' + encodeURIComponent(audiourl);
          }
          if (options) {
              callurl += '&options=' + encodeURIComponent(options);
          }

          if (stream && this.pc) {
              stream.getTracks().forEach(track => {
                  this.pc?.addTrack(track, stream);
              });
          }

          this.earlyCandidates.length = 0;

          if (this.pc) {
              this.pc.createOffer(this.mediaConstraints).then(
                  sessionDescription => {
                      console.log('Create offer:' + JSON.stringify(sessionDescription));

                      if (prefmime !== undefined) {
                          const [prefkind] = prefmime.split('/');
                          if (this.pc) {
                              const transceivers = this.pc.getTransceivers()
                                  .filter(transceiver => transceiver.receiver.track?.kind === prefkind);
                              
                              // Use static method if available, otherwise skip codec preferences
                              if (RTCRtpReceiver.getCapabilities) {
                                  try {
                                      const capabilities = RTCRtpReceiver.getCapabilities(prefkind as 'audio' | 'video');
                                      if (capabilities && capabilities.codecs) {
                                          const codecs = capabilities.codecs;
                                          const preferedCodecs = codecs.filter(codec => codec.mimeType === prefmime);
                                          
                                          transceivers.forEach(tcvr => {
                                              if (tcvr.setCodecPreferences !== undefined) {
                                                  tcvr.setCodecPreferences(preferedCodecs);
                                              }
                                          });
                                      }
                                  } catch (e) {
                                      console.log('Codec preferences not supported:', e);
                                  }
                              }
                          }
                      }

                      if (this.pc) {
                          this.pc.setLocalDescription(sessionDescription).then(
                              () => {
                                  fetch(callurl, { method: 'POST', body: JSON.stringify(sessionDescription) })
                                      .then(this._handleHttpErrors)
                                      .then(response => response.json())
                                      .catch(error => this.onError('call ' + error))
                                      .then(response => this.onReceiveCall(response))
                                      .catch(error => this.onError('call ' + error));
                              },
                              error => {
                                  console.log('setLocalDescription error:' + JSON.stringify(error));
                              }
                          );
                      }
                  },
                  error => {
                      console.log('Create offer error:' + JSON.stringify(error));
                  }
              );
          }
      } catch (e) {
          this.disconnect();
          console.log('connect error: ' + e);
      }
  }

  private getIceCandidate(): void {
      if (this.pc) {
          fetch(this.srvurl + '/api/getIceCandidate?peerid=' + this.pc.id)
              .then(this._handleHttpErrors)
              .then(response => response.json())
              .then(response => this.onReceiveCandidate(response))
              .catch(error => this.onError('getIceCandidate ' + error));
      }
  }

  private createPeerConnection(pcConfig: any): void {
      console.log('createPeerConnection  config: ' + JSON.stringify(pcConfig));
      this.pc = new RTCPeerConnection(pcConfig);
      
      if (this.pc) {
          this.pc.id = Math.random().toString(36).substring(7);

          this.pc.onicecandidate = evt => this.onIceCandidate(evt);
          this.pc.ontrack = evt => this.onTrack(evt);
          this.pc.oniceconnectionstatechange = evt => {
              console.log('oniceconnectionstatechange  state: ' + this.pc?.iceConnectionState);
              if (this.videoElement && this.pc) {
                  if (this.pc.iceConnectionState === 'connected') {
                      this.videoElement.style.opacity = '1.0';
                  } else if (this.pc.iceConnectionState === 'disconnected') {
                      this.videoElement.style.opacity = '0.25';
                  } else if (this.pc.iceConnectionState === 'failed' || this.pc.iceConnectionState === 'closed') {
                      this.videoElement.style.opacity = '0.5';
                  } else if (this.pc.iceConnectionState === 'new') {
                      this.getIceCandidate();
                  }
              }
          }
          this.pc.ondatachannel = evt => {
              console.log('remote datachannel created:' + JSON.stringify(evt));

              evt.channel.onopen = function() {
                  console.log('remote datachannel open');
                  this.send('remote channel openned');
              }
              evt.channel.onmessage = function(event) {
                  console.log('remote datachannel recv:' + JSON.stringify(event.data));
              }
          }
          this.pc.onicegatheringstatechange = () => {
              if (this.pc && this.pc.iceGatheringState === 'complete') {
                  const recvs = this.pc.getReceivers();

                  recvs.forEach(recv => {
                      if (recv.track && recv.track.kind === 'video') {
                          console.log('codecs:' + JSON.stringify(recv.getParameters()));
                      }
                  });
              }
          }

          try {
              const dataChannel = this.pc.createDataChannel('ClientDataChannel');
              dataChannel.onopen = function() {
                  console.log('local datachannel open');
                  this.send('local channel openned');
              }
              dataChannel.onmessage = function(evt) {
                  console.log('local datachannel recv:' + JSON.stringify(evt.data));
              }
          } catch (e) {
              console.log('Cannor create datachannel error: ' + e);
          }

          console.log('Created RTCPeerConnnection with config: ' + JSON.stringify(pcConfig));
      }
  }

  private onIceCandidate(event: RTCPeerConnectionIceEvent): void {
      if (event.candidate && this.pc) {
          if (this.pc.currentRemoteDescription) {
              this.addIceCandidate(this.pc.id || '', event.candidate);
          } else {
              this.earlyCandidates.push(event.candidate);
          }
      } else {
          console.log('End of candidates.');
      }
  }

  private addIceCandidate(peerid: string, candidate: RTCIceCandidate): void {
      fetch(this.srvurl + '/api/addIceCandidate?peerid=' + peerid, {
          method: 'POST',
          body: JSON.stringify(candidate)
      })
          .then(this._handleHttpErrors)
          .then(response => response.json())
          .then(response => {
              console.log('addIceCandidate ok:' + response);
          })
          .catch(error => this.onError('addIceCandidate ' + error));
  }

  private onTrack(event: RTCTrackEvent): void {
      console.log('Remote track added:' + JSON.stringify(event));

      if (this.videoElement) {
          this.videoElement.srcObject = event.streams[0];
          const promise = this.videoElement.play();
          if (promise !== undefined) {
              promise.catch(error => {
                  console.warn('error:' + error);
                  this.videoElement?.setAttribute('controls', 'true');
              });
          }
      }
  }

  private onReceiveCall(dataJson: any): void {
      console.log('offer: ' + JSON.stringify(dataJson));
      if (this.pc) {
          const descr = new RTCSessionDescription(dataJson);
          this.pc.setRemoteDescription(descr).then(
              () => {
                  console.log('setRemoteDescription ok');
                  while (this.earlyCandidates.length) {
                      const candidate = this.earlyCandidates.shift();
                      if (candidate && this.pc) {
                          this.addIceCandidate(this.pc.id || '', candidate);
                      }
                  }

                  this.getIceCandidate();
              },
              error => {
                  console.log('setRemoteDescription error:' + JSON.stringify(error));
              }
          );
      }
  }

  private onReceiveCandidate(dataJson: any): void {
      console.log('candidate: ' + JSON.stringify(dataJson));
      if (dataJson && this.pc) {
          for (let i = 0; i < dataJson.length; i++) {
              const candidate = new RTCIceCandidate(dataJson[i]);

              console.log('Adding ICE candidate :' + JSON.stringify(candidate));
              this.pc.addIceCandidate(candidate).then(
                  () => {
                      console.log('addIceCandidate OK');
                  },
                  error => {
                      console.log('addIceCandidate error:' + JSON.stringify(error));
                  }
              );
          }
      }
  }

  private onError(status: string): void {
      console.log('onError:' + status);
  }
}

export default WebRtcStreamer;    