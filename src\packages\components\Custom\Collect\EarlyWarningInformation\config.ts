import { PublicConfigClass } from '@/packages/public'
import { EarlyWarningInformation } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
export const includes = ['legend', 'xAxis', 'yAxis', 'grid']
import { chartInitConfig } from '@/settings/designSetting'
import dataJson from './data.json'

// 数据配置
export const option = {
  titleValue: '预警信息',
  dataset: dataJson.certificateList,
  tableDataType: 'DataForMonth',
}
// 导出配置
export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = EarlyWarningInformation.key
  public chartConfig = cloneDeep(EarlyWarningInformation)
  public attr = { ...chartInitConfig, w: 596, h: 308, zIndex: -1 }
  // 图表配置项
  public option = cloneDeep(option)
}
