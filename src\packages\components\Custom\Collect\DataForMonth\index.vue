<template>
  <div :style="`width:${w}px;height:${h}px;`" class="early-warning-information c-white">
    <div class="early-warning-information-text">
      <div class="label">{{option.value.titleValue}}</div>
      <div class="fee">{{ totalData.length ? formatAmount(totalData[0].fee || 0) : '--' }}</div>
      <div class="basis">月环比
        <span>{{ totalData.length ? totalData[0].basis : '--' }}</span>
        <img v-if="totalData.length && totalData[0].value > 0" src="@/assets/images/chart/custom/up_arrow_icon.png" alt="" srcset="">
        <img v-if="totalData.length && totalData[0].value < 0" src="@/assets/images/chart/custom/down_arrow_icon.png" alt="" srcset="">
      </div>
    </div>
    <div>
      <img src="@/assets/images/chart/custom/data_for_month.png" style="width: 80px; height: 66px;">
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
// import { apiBackgroundFundSupervisionBigShieldMonthProfitData } from '@/api/path'
import { useRoute } from 'vue-router'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { apiGetMonthTotalDetail, apiBackgroundFundSupervisionBigShieldDietMonthRevenueExpendProfit, apiBackgroundFundSupervisionBigShieldMonthProfitData } from '@/api/path'
import { formatAmount } from '@/utils'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)
console.log('看看initOptions', initOptions)

const chartEditStore = useChartEditStore()
const selectValue = chartEditStore.getControlGlobalConfigByKey('ChooseTree')

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);
// 配置
const route = useRoute()
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option),
  channelId: Number(route.query.channel_id),
  selectValue: 0,
  orgId :Number(route.query.org_id),
})

const totalData = ref<any>([])

// 数据获取
const getData = async () => {
  console.log(option.value.tableDataType,'tableDataType的值');
  let api: any
  let params: any = {}
  switch (option.value.tableDataType) {
    case 'DataForMonth':
      // 基础信息大屏-本月营收
      api = apiBackgroundFundSupervisionBigShieldMonthProfitData
      params.channel_id = option.channelId
      if (route.query.role_id) {
        params.role_id = Number(route.query.role_id)
      }
      break
      // 膳食经费资金监管屏-本月营收
    case 'mealExpensesScreen':
      api = apiBackgroundFundSupervisionBigShieldDietMonthRevenueExpendProfit
      params.channel_id = option.channelId
      params.org_id = option.selectValue ? option.selectValue : option.orgId ? option.orgId : undefined
      params.org_list = option.selectValue ? [option.selectValue] : option.orgId ? [option.orgId] : []
      if (route.query.role_id) {
        params.role_id = Number(route.query.role_id)
      }
      break
  }
  const res = await api(params)
  if (res && res.data) {
    totalData.value = []
    let data = res.data || {}
    let consumePriceChain = res.data && res.data.consume_price_chain || []
    if (consumePriceChain) {
      let obj = {
        fee: (data.consume_price > 100000 || data.consume_price < -100000) ? (data.consume_price / 10000).toFixed(0) + '万' : data.consume_price,
        basis: data.consume_price_chain,
        value: Number(data.consume_price_chain.slice(0, data.consume_price_chain.length - 1))
      }
      totalData.value.push(obj)
    }
  }
}

// 页面加载
onMounted(() => {
  console.log("onMounted")
  if (option.value.tableDataType === 'DataForMonth') {
    totalData.value = [{
      fee: 45720050,
      basis: "6.2%",
      value: 1,
    }]
    console.log(999666, totalData.value)
  } else {
    // getData()
  }
})

// dataset 无法变更条数的补丁
watch(
  () => props.chartConfig.option,
  (newData: any) => {
    console.log("newData有新得值更新", newData);
    option.value = cloneDeep(newData)
  },
  {
    immediate: true,
    deep: true
  }
)

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    const selectValue = chartEditStore.getControlGlobalConfigByKey('ChooseTree')
    if (selectValue?.value === null) {
      option.selectValue = 0
    } else {
      option.selectValue = selectValue?.value
    }
    console.log('ChooseSelect11', selectValue)
    // getData()
  },
  {
    immediate: true,
    deep: true
  }
)


</script>

<style scoped lang="scss">
.early-warning-information {
  background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
  border: 1px solid #00A3FF1A;
  border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
  // padding: 15px 20px 10px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-around;
  &-text {
    .label {
      color: #FFF;
      font-size: 22px;
      font-weight: 400;
      line-height: 100%; /* 22px */
    }
    .fee {
      color: #0BF9FE;
      font-size: 32px;
      font-weight: 700;
      line-height: 100%; /* 36px */
      margin-bottom: 10rpx;
      padding: 5px 0;
    }
    .basis {
      color: rgba(255, 255, 255, 0.60);
      font-size: 14px;
      font-weight: 400;
      line-height: 100%; /* 14px */
      span {
        color: #FFF;
        font-size: 16px;
        font-weight: 500;
        line-height: 100%; /* 16px */
      }
    }
  }
}
</style>