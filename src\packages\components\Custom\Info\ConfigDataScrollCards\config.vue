<template>
  <div class="">
    <collapse-item name="基础配置" :expanded="true">
      <setting-item-box name="标题" :alone="false" :leftStyle="leftStyle">
        <n-input
          v-model:value="optionData.titleValue"
          type="text"
          placeholder="请输入标题"
          class="input-tag"
          show-count
          :maxlength="12">
        </n-input>
      </setting-item-box>

      <setting-item-box name="选择类型">
        <n-select
          size="small"
          style="width: 200px"
          v-model:value="optionData.cardDataType"
          :options="selectCardOptions"
          @update:value="selectCardValueHandle"
        />
      </setting-item-box>
    </collapse-item>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue'
import { PickCreateComponentType } from '@/packages/index.d'
import { GlobalThemeJsonType } from '@/settings/chartThemes/index'
import { CollapseItem, SettingItemBox } from '@/components/Pages/ChartItemSetting'
import dataForMonthImage from '@/assets/images/chart/custom/data_for_month.png'
import inboundPriceImage from '@/assets/images/chart/custom/inbound_price.png'
import outboundPriceImage from '@/assets/images/chart/custom/outbound_price.png'
import deviceImage from '@/assets/images/chart/custom/food_device.png'
import keepSampleImage from '@/assets/images/chart/custom/food_keep_sample.png'
import takeSampleImage from '@/assets/images/chart/custom/food_take_sample.png'

const props = defineProps({
  optionData: {
    type: Object as PropType<GlobalThemeJsonType>,
    required: true
  },
  chartAttr: {
    type: Object as PropType<Omit<PickCreateComponentType<'attr'>, 'node' | 'conNode'>>,
    required: true
  }
})

// 卡片配置选项
const selectCardOptions = [
  {
    label: '当日陪餐',
    value: "accompany_meal",
    data: [],
    width: 288,
    height: 178,
    scrollHeight: 128,
    limiScrollNum: 3,
    showTableHead: true,
    showDivider: true,
    dateType: '',
    dateTypeOptions: [],
    displayMode: 'accompany_meal',
    colorList: ['#17E9AD', '#FB8253', '#20CBFF', '#17E9AD', '#FB8253', '#20CBFF'],
    apiConfig: {
      apiFunction: 'apiBackgroundFundSupervisionBigShieldMealAccompanyData',
      apiParams: ['org_id']
    }
  }
]

const selectCardValueHandle = (value: any) => {
  const selectedOption = selectCardOptions.find(item => item.value === value)
  if (selectedOption) {
    props.optionData.titleValue = selectedOption.label
    props.optionData.cardDataType = selectedOption.value
    props.optionData.dataset = selectedOption.data
    props.optionData.cardConfig = selectedOption
    props.chartAttr.w = selectedOption.width ? selectedOption.width : 673
    props.chartAttr.h = selectedOption.height ? selectedOption.height : 200
  }
}

// 初始化时设置默认配置
const initCardSetting = () => {
  const currentType = props.optionData.cardDataType
  if (currentType) {
    selectCardValueHandle(currentType)
  }
}

// 组件挂载时初始化
initCardSetting()

// 自定义左侧样式
const leftStyle: any = {
  width: "100px"
}
</script>

<style scoped lang="scss">
.input-tag {
  width: 200px;
}
</style>

<style scoped lang="scss">
.configurable-data-cards-config {
  .input-tag {
    width: 200px;
  }
  
  .data-items-config {
    .data-item {
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      padding: 12px;
      margin-bottom: 10px;
      
      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        font-weight: bold;
      }
      
      .item-config {
        .config-row {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          
          label {
            width: 80px;
            margin-right: 10px;
            font-size: 12px;
          }
          
          .n-input {
            flex: 1;
          }
        }
      }
    }
  }
  
  .color-config {
    .color-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      label {
        width: 60px;
        margin-right: 10px;
        font-size: 12px;
      }
    }
  }
}
</style>
