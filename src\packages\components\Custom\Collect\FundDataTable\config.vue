<template>
  <div class="">
    <collapse-item name="基础配置" :expanded="true">
      <setting-item-box name="标题" :alone="false">
        <n-input v-model:value ="optionData.titleValue" type="text" placeholder="请输入标题" class="input-tag" show-count :maxlength="12"></n-input>
      </setting-item-box>
      <setting-item-box name="选择类型">
        <n-select
          size="small"
          style="width: 200px"
          v-model:value="optionData.tableDataType"
          :options="selectTableOptions"
          @update:value="selectTableValueHandle"
        />
      </setting-item-box>
    </collapse-item>
  </div> 
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue'
import { PickCreateComponentType } from '@/packages/index.d'
import { GlobalThemeJsonType } from '@/settings/chartThemes/index'
import { option } from './config'
import { CollapseItem, SettingItemBox } from '@/components/Pages/ChartItemSetting'
import dataJson from './data.json'

const props = defineProps({
  optionData: {
    type: Object as PropType<GlobalThemeJsonType>,
    required: true
  },
  chartAttr: {
    type: Object as PropType<Omit<PickCreateComponentType<'attr'>, 'node' | 'conNode'>>,
    required: true
  }
})

// 默认表类型
const selectTableOptions = [
  {
    label: '收支利润排行',
    value: "profit",
    data: dataJson.profitList.monthData
  },
  {
    label: '人均消费排行-本月',
    value: "perCapita",
    data: dataJson.perCapitaList
  },
  {
    label: '成本支出明细',
    value: "costExpenditureDetails",
    data: dataJson.costExpenditureDetailsList.monthData
  },
  {
    label: '原材料占比排行-本月',
    value: "proportionOfRawMaterial",
    data: dataJson.proportionOfRawMaterialList
  },
  {
    label: '食堂就餐消费金额',
    value: "canteenConsumption",
    scrollHeight: 230,
    data: dataJson.canteenConsumptionList.fanyingyizhong.weekData
  },
  {
    label: '原材料采购明细-本月',
    value: "purchaseDetails",
    data: dataJson.purchaseDetailsList.fanyingyizhong
  },
  {
    label: '原材料出入库明细-本月',
    value: "InventoryDetails",
    data: dataJson.InventoryDetailsList.fanyingyizhong
  },
  {
    label: '收入情况',
    value: "incomeSituation",
    data: dataJson.incomeSituationList,
    height: 875,
    scrollHeight: 747
  },
  {
    label: '收入情况-本月',
    value: "incomeSituation-month",
    data: dataJson.incomeSituationList,
  },
  {
    label: '收入情况-本年',
    value: "incomeSituation-year",
    data: dataJson.incomeSituationList,
  },
  {
    label: '负债情况',
    value: "debtSituation",
    data: dataJson.debtSituationList
  },
  {
    label: '健康证公示',
    value: "healthCertificate",
    data: dataJson.healthCertificateList,
    height: 368,
    scrollHeight: 240
  },
  {
    label: '今日巡查结果',
    value: "todayInspection",
    data: dataJson.todayInspectionList,
    height: 248,
    scrollHeight: 120
  },
  {
    label: '食材信息公示',
    value: "foodInformation",
    data: dataJson.foodInformationAllData.fanyingyizhong
  },
  {
    label: '供餐信息',
    value: "mealInformation",
    data: dataJson.mealInformationList,
    height: 228,
    scrollHeight: 120
  },
  {
    label: '收入排行',
    value: "incomeRanking",
    data: dataJson.incomeRanking,
    height: 502,
    scrollHeight: 359
  }
]

const selectTableValueHandle = (value: any) => {
  selectTableOptions.map(item => {
    console.log('看看optionData', props.optionData)
    if (item.value === value) {
      props.optionData.titleValue = item.label
      props.optionData.dataset = item.data
      props.chartAttr.h = item.height ? item.height : 440
      props.optionData.scrollHeight = item.scrollHeight ? item.scrollHeight : 305
      props.optionData.tableDataType = item.value
    }
  })
}

</script>
<style scoped lang="scss">
 .input-tag{
  width: 200px;
 }
</style>