<template>
  <div class="">
    <collapse-item name="基础配置" :expanded="true">
      <setting-item-box name="标题" :alone="false">
        <n-input v-model:value ="optionData.titleValue" type="text" placeholder="请输入标题" class="input-tag" show-count :maxlength="12"></n-input>
      </setting-item-box>
      <!-- <setting-item-box name="单位">
        <n-input v-model:value ="optionData.unitValue" type="text" placeholder="请输入单位" class="input-tag" show-count :maxlength="10"></n-input>
      </setting-item-box> -->
      <setting-item-box name="选择类型">
        <n-select
          size="small"
          style="width: 200px"
          v-model:value="optionData.tableDataType"
          :options="selectTableOptions"
          @update:value="selectTableValueHandle"
        />
      </setting-item-box>
    </collapse-item>

  </div> 
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue'
import { GlobalThemeJsonType } from '@/settings/chartThemes/index'
import { CollapseItem, SettingItemBox } from '@/components/Pages/ChartItemSetting'

const props = defineProps({
  optionData: {
    type: Object as PropType<GlobalThemeJsonType>,
    required: true
  }
})

// 默认表类型
const selectTableOptions = [
  {
    label: '膳食经费资金监管屏',
    title: '收入分布',
    value: "DietDashboard"
  },
]

// 选择类型
const selectTableValueHandle = (value: any) => {
  selectTableOptions.map(item => {
    if (item.value === value) {
      props.optionData.titleValue = item.title
      props.optionData.tableDataType = item.value
    }
  })
}
</script>

<style scoped lang="scss">
 .input-tag{
  width: 200px;
 }
</style>