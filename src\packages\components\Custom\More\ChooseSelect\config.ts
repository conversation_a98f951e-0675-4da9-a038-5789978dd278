import { PublicConfigClass } from '@/packages/public'
import { ChooseSelect } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import { chartInitConfig } from '@/settings/designSetting'

export const option = {
  titleValue: "",
  defaultValue: "shangyouerzhong",
  paramsId:'', //这个是要调接口的时候所要传的key值名称，默认是空
  currentIndex:0, // 默认点击
  relativeIds:[],
  borderColor: '#18FEFE',
  dataList:[
    {
      label:'上犹第二中学',
      value:'shangyouerzhong'
    },
    {
      label:'上犹第一中学',
      value:'shangyouyizhong'
    },
    {
      label:'安远实验中学',
      value:'anyuan'
    },
    {
      label:'章贡第一附属小学',
      value:'zhanggong'
    },
    {
      label:'大余实验小学',
      value:'dayu'
    }
  ]
}

export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = ChooseSelect.key
  public chartConfig = cloneDeep(ChooseSelect)
  public attr = {...chartInitConfig, w:244, h:60, zIndex:-1}
  // 图表配置项
  public option = cloneDeep(option)
}
