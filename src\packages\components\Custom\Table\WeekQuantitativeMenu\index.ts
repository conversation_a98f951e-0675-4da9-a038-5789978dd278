import { ConfigType, PackagesCategoryEnum, ChartFrameEnum } from '@/packages/index.d'
import { CustomCategoryEnum, CustomCategoryEnumName } from '../../index.d'

export const WeekQuantitativeMenu: ConfigType = {
  key: 'WeekQuantitativeMenu',
  chartKey: 'VWeekQuantitativeMenu',
  conKey: 'VCWeekQuantitativeMenu',
  title: '本周带量食谱',
  category: CustomCategoryEnum.Table,
  categoryName: CustomCategoryEnumName.Table,
  package: PackagesCategoryEnum.CUSTOM,
  chartFrame: ChartFrameEnum.COMMON,
  image: 'fund_data_table.png'
}
