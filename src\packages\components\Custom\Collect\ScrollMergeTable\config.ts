import { PublicConfigClass } from '@/packages/public'
import { ScrollMergeTable } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
export const includes = ['legend', 'xAxis', 'yAxis', 'grid']
import { chartInitConfig } from '@/settings/designSetting'
import dataJson from './data.json'
// 数据配置
export const option = {
  titleValue: '本周带量食谱公示',
  isScroll: true,
  dataList: dataJson
}
// 导出配置
export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = ScrollMergeTable.key
  public chartConfig = cloneDeep(ScrollMergeTable)
  public attr = { ...chartInitConfig, w: 596, h: 440, zIndex: -1 }
  // 图表配置项
  public option = cloneDeep(option)
}
