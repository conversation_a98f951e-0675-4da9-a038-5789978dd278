<template>
  <div :style="`width:${w}px;height:${h}px;`" class="organ-structure">
    <fund-head :title="option.value.titleValue" :style="`width:${w}px`" type="orgs" @selectHandle="handlerClickOrgs">
    </fund-head>
    <div class="warning-list" :style="`width:${w}px;height:${h}px;`" v-loading="dataLoading">
      <vue3-tree-org :data="dataTree" :horizontal="true" :collapsable="true" :label-style="style" :only-one-node="false"
        :default-expand-level="3" :tool-bar="false" :props="treeProps" v-if="isTree">
        <template v-slot="{ node }">
          <div class="tree-org-node__text node-label">
            <div class="custom-img-tag">
              <img :src="getImg(node)" class="custom-img" />
              <div class="custom-label">{{ node.label }}</div>
            </div>
            <div class="custom-name">{{ node.id }}</div>
            <div class="custom-phone">{{ node.pid }}</div>
          </div>
        </template>
      </vue3-tree-org>
      <div v-else  :style="`height:${h-60}px;overflow:hidden;`">
        <vue3-seamless-scroll class="scroll" v-model='scollModel' :list="dataTree" :step="0.5" direction="up"
        :hover="true" :limit-scroll-num="2" :wheel="true">
        <div class="level-tree">
        <div class="list-level" v-for="(item, index) in dataTree" :key="index">
          <div class="level-tag">{{ item.levelName }}</div>
          <div class="level-list">
            <div v-for="(node, subIndex) in item.personList" :key="subIndex" class="level-img">
              <div>
                <img :src="node.face_url" class="img-box" />
              </div>
              <div class="img-name">{{ node.name }}</div>
              <div class="img-phone">{{ node.phone }}</div>
            </div>
          </div>
        </div>
        </div>
      </vue3-seamless-scroll>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, watchEffect, onMounted, reactive } from 'vue'
import config from './config'
import { FundHead } from '@/components/FundHead'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { OrganStructure } from './index'
import cloneDeep from 'lodash/cloneDeep'
import { apiBackgroundFundSupervisionBigShieldOrganizationalStructureListPost } from '@/api/path'
import { useRoute } from 'vue-router'
import { parseTime } from '@/utils/time'
import dataJson from "./data.json"
import { clone } from 'lodash'
import icFuZeRen from "@/assets/images/chart/custom/ic_fu_ze_ren.png"
import icAnQuanZongJian from "@/assets/images/chart/custom/ic_an_quan_zong_jian.png"
import icHuangBo from "@/assets/images/chart/custom/ic_huang_bo.png"
import icCaoShuYi from "@/assets/images/chart/custom/ic_cao_shu_yi.png"
import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'

const route = useRoute()

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
// 长宽
const { w, h } = toRefs(props.chartConfig.attr)
const contentHeight = ref((h.value - 42))
console.log("w", w, "h", h);
// 控件的key
const componentKey = OrganStructure.key
// 配置
const option = reactive({
  value: cloneDeep(props.chartConfig.option)
})
console.log("option", option);
// 数据列表
const dataList = ref<Array<any>>(option.value.dataList)
// 数据加载中
const dataLoading = ref(false)

const dataTree = ref<Array<any>>()
console.log("dataTree", dataTree);
const treeProps = {
  id: 'name', pid: 'phone', label: 'label', expand: 'expand', children: 'children', name: 'name', phone: 'phone'
}
const style = {
  background: "transparent",
  color: "#fff",
}
//  保存的数据
const chartEditStore = useChartEditStore()

const isTree = option.value.isTree
console.log("isTree", isTree);

// 设置的数据类型
const dataSetType = ref()
// 区域改变
const handlerClickArea = (item: any) => {
  console.log("item", item);
}
// 组织id
const orgId = ref('')
// 组织数据
type orgsLevelData = {
  id: string,
  phone: string,
  face_url: string,
  name: string,
  job_title: string,
  level: Array<any>
}
// 
const scollModel = ref(true)
const getImg = (node: any) => {
  console.log("node", node);
  let id = node.id || ''
  switch (id) {
    case '于进':
      return icFuZeRen
    case '周华庆':
      return icAnQuanZongJian
    case '黄波':
      return icHuangBo
    case '曹淑仪':
      return icCaoShuYi
  }
}
// 初始化数据
const initData = async () => {
  // 获取数据
  let type = props.chartConfig.option.dataType
  dataSetType.value = type
  console.log("initData", dataSetType.value);
  if (!orgId.value) {
    return
  }
  getDataList()
}
// 获取数据
const getDataList = async (type?: any) => {
  dataLoading.value = true
  let api;
  let params = {
    channel_id: option.value.selectValue ? option.value.selectValue : Number(route.query.channel_id),
    org_id: orgId.value,
    role_id: Number(route.query.role_id)
  }
  switch (dataSetType.value) {
    // schoolOrganStructure
    case 'schoolOrganStructure':
      api = apiBackgroundFundSupervisionBigShieldOrganizationalStructureListPost(params)
      break;
  }
  let res = await api
  dataLoading.value = false
  if (res && res.code === 0) {
    console.log("apiBackgroundFundSupervisionBigShieldOrganizationalStructureListPost", res, dataList.value)
    const data = res.data || []
    if (dataSetType.value === 'schoolOrganStructure') {
      console.log("data", data)
      dataTree.value = getDataByLevel(data)
      console.log("dataTree", dataTree.value)
    }

  } else {
    dataList.value = []
  }
}
// 点击组织架构
const handlerClickOrgs = (item) => {
  console.log("handlerClickOrgs", item)
  orgId.value = item
  initData()
}
// 获取组织架构数据
const getDataByLevel = (dataList: Array<orgsLevelData>) => {
  console.log("getDataByLevel", dataList)
  if (!dataList) {
    return []
  }
  let level1 = dataList.filter(item => {
    let leveStr = item.level && Array.isArray(item.level) ? item.level.join('') : ''
    return leveStr.indexOf('食品安全负责人') > -1
  })
  let level2 = dataList.filter(item => {
    let leveStr = item.level && Array.isArray(item.level) ? item.level.join('') : ''
    return leveStr.indexOf('食品安全总监') > -1
  })
  let level3 = dataList.filter(item => {
    let leveStr = item.level && Array.isArray(item.level) ? item.level.join('') : ''
    return leveStr.indexOf('食品安全员') > -1
  })
  let newList:Array<any> = []
  if( level1.length > 0) {
    newList.push({ levelName: '食品安全负责人', personList: level1 })
  }
  if( level2.length > 0) {
    newList.push({ levelName: '食品安全总监', personList: level2 })
  }
  if( level3.length > 0) {
    newList.push({ levelName: '食品安全员', personList: level3 })
  }
  console.log("getDataByLevel", newList);
  return newList
}


onMounted(() => {

})


// watch(
//   () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
//   (newData: any) => {
//     if (newData) {
//       // 如果有值，并且selecctValue为空
//       if (!option.value.selectValue) {
//         option.value.selectValue = newData.value
//         initData()
//       } else if (option.value.selectValue !== newData.value) {
//         option.value.selectValue = newData.value
//         initData()
//       }
//     }
//   },
//   {
//     immediate: false,
//     deep: true
//   }
// )

</script>
<style scoped lang="scss">
.organ-structure {
  border: 1px solid #082F50;

  .warning-list {}

  .list-tag {
    display: inline-flex;
    flex-direction: column;
    position: relative;
    padding: 0 20px 0 0;

    .tag-name {
      font-size: 16px;
      height: 16px;
      color: #fff;
    }
  }

  .tag-index {
    height: 42px;
    color: #0BF9FE;
    font-size: 36px;
    text-align: left;
  }

  .ver-line {
    width: 1px;
    height: 52px;
    background: #003C60;
    position: absolute;
    right: 0;
  }

  .zm-tree-org {
    background: transparent !important;
  }

  .tree-org-node__text {
    display: flex;
    flex-direction: column;
    align-items: center;

    .custom-img-tag {
      border: 1px solid #02A9FF80;
      background: #0E48724D;
      width: 60px;
      height: 74px;
      position: relative;
      padding: 2px;

      .custom-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 9;
      }

      .custom-label {
        color: #FFFFFF;
        min-height: 20px;
        background: #246BC0;
        font-size: 11px;
        text-align: center;
        position: absolute;
        bottom: 2px;
        left: 2px;
        right: 2px;
        z-index: 10;
      }

    }

    .custom-name {
      font-size: 12px;
      color: #fff;
    }

    .custom-phone {
      font-size: 12px;
      color: #7F8C9A;
    }

  }
  .scroll {
      overflow: hidden;
      height: 300px;
    }
  .level-tree {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  .list-level {

      .level-tag {
        color: #26BDFF;
        font-size: 20px;
        text-align: left;
        padding: 0 10px ;
        margin: 5px auto;
      }
      .level-list {
        display: inline-flex;
        flex-wrap: wrap;
        .level-img {
          margin: 2px;
          display: inline-flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .img-box {
            border: 1px solid #02A9FF80;
            background: #0E48724D;
            width: 60px;
            height: 74px;
            position: relative;
            padding: 2px;
          }
          .img-name {
            color: #fff;
            text-align: center;
          }
          .img-phone {
            color: #fefefe;
          }
        }
      }
    }
}
</style>