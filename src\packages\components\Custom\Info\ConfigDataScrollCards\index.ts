import { ConfigType, PackagesCategoryEnum, ChartFrameEnum } from '@/packages/index.d'
import { CustomCategoryEnum, CustomCategoryEnumName } from '../../index.d'

export const ConfigDataScrollCards: ConfigType = {
  key: 'ConfigDataScrollCards',
  chartKey: 'VConfigDataScrollCards',
  conKey: 'VCConfigDataScrollCards',
  title: '可滚动信息卡片',
  category: CustomCategoryEnum.Info,
  categoryName: CustomCategoryEnumName.Info,
  package: PackagesCategoryEnum.CUSTOM,
  chartFrame: ChartFrameEnum.COMMON,
  image: 'configurable_data_cards.png'
}
