<template>
  <div :style="`width:${w}px;height:${h}px;`" class="bar-gradients-fund c-white">
    <!--标题-->
    <fund-head
      :title="option.value.titleValue"
      :style="`width:${w}px`">
    </fund-head>
    <div>
      <!--内容-->
      <div class="bar-wrap" :style="`width:${w}px;height:${h - 50}px;`" id='BarGradientsFund'>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, PropType, shallowReactive, toRefs, onMounted, reactive } from 'vue'
import config, { includes } from './config'
import cloneDeep from 'lodash/cloneDeep'
import { isPreview } from '@/utils'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import * as echarts from 'echarts'
import 'echarts-gl'
import  FundHead from '@/components/FundHead/index.vue'
import dataJson from './data.json'
import { apiGetCanteenAnnualProfitsRank } from '@/api/path'
import { useRoute } from 'vue-router'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'


const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

const route = useRoute()
const chartEditStore = useChartEditStore()

//  长宽获取
const { w, h } = toRefs(props.chartConfig.attr)
console.log("w", w, "h", h);
// 配置
const option = shallowReactive({
  value: cloneDeep(props.chartConfig.option),
  orgId: Number(route.query.org_id),
  selectValue: 0
})

// 获取数据
const getSeriesData = async () => {
  const res = await apiGetCanteenAnnualProfitsRank({
    org_id: option.selectValue ? option.selectValue : option.orgId
  })
  if (res && res.data && res.data.length) {
    option.value.chartOpts.series[0].data = []
    res.data.map(item => {
      let profit = Number(item.profit.slice(0, item.profit.length - 1))
      option.value.chartOpts.series[0].data.push(profit)
    })
    initData()
  }
}

// 设置bar颜色 
const setBarColor = () => {
  let itemStyle1 = {
    normal: {
      color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
        {
          offset: 0,
          color: '#0085FF'
        },
        {
          offset: 1,
          color: '#0085FF00'
        }
      ])
    }
  }
  option.value.chartOpts.series[0].itemStyle = Object.assign(option.value.chartOpts.series[0].itemStyle, itemStyle1)
}

// 初始化数据
const initData = () => {
  setBarColor()
  const BarGradientsFund = document.getElementById('BarGradientsFund')
  if (BarGradientsFund) {
    let myChart = echarts.init(BarGradientsFund)
    console.log("option", option);
    myChart.setOption(option.value.chartOpts)
  }

}

// 页面加载
onMounted(() => {
  getSeriesData()
})

// dataset 无法变更条数的补丁
watch(
  () => props.chartConfig.option,
  (newData: any) => {
    console.log("newData有新得值更新", newData);
    option.value = cloneDeep(newData)
    // nextTick(() => {
    //   getSeriesData()
    // })
  },
  {
    immediate: true,
    deep: true
  }
)

watch(
  () => chartEditStore.getControlGlobalConfigByKey('ChooseTree'),
  (newData: any) => {
    const selectValue = chartEditStore.getControlGlobalConfigByKey('ChooseTree')
    if (selectValue?.value === null) {
      option.selectValue = 0
    } else {
      option.selectValue = selectValue?.value
    }
    getSeriesData()
  },
  {
    immediate: true,
    deep: true
  }
)

</script>

<style scoped lang="scss">
.bar-gradients-fund {
  position: relative;
  color: #fff;

  .title {
    font-size: 22rpx;
    color: #fff;
  }

  .bar-wrap {
    position: relative;
    background: linear-gradient(90deg, rgba(0, 133, 255, 0.15) 0%, rgba(0, 163, 255, 0.015) 100%);
    border: 1px solid;
    border-image: linear-gradient(270deg, rgba(0, 170, 255, 0.15) 0%, rgba(0, 170, 255, 0.7))1;
  }

}
</style>