import { ConfigType, PackagesCategoryEnum, ChartFrameEnum } from '@/packages/index.d'
import { CustomCategoryEnum, CustomCategoryEnumName } from '../../index.d'

export const GeneralDataTable: ConfigType = {
  key: 'GeneralDataTable',
  chartKey: 'VGeneralDataTable',
  conKey: 'VCGeneralDataTable',
  title: '资金通用报表',
  category: CustomCategoryEnum.Table,
  categoryName: CustomCategoryEnumName.Table,
  package: PackagesCategoryEnum.CUSTOM,
  chartFrame: ChartFrameEnum.COMMON,
  image: 'fund_data_table.png'
}
