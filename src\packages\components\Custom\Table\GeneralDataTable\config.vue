<template>
  <div class="">
    <collapse-item name="基础配置" :expanded="true">
      <setting-item-box name="标题" :alone="false">
        <n-input v-model:value ="optionData.titleValue" type="text" placeholder="请输入标题" class="input-tag" show-count :maxlength="12"></n-input>
      </setting-item-box>
      <setting-item-box name="选择类型">
        <n-select
          size="small"
          style="width: 200px"
          v-model:value="optionData.tableDataType"
          :options="selectTableOptions"
          @update:value="selectTableValueHandle"
        />
      </setting-item-box>
    </collapse-item>
  </div> 
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue'
import { PickCreateComponentType } from '@/packages/index.d'
import { GlobalThemeJsonType } from '@/settings/chartThemes/index'
import { option } from './config'
import { CollapseItem, SettingItemBox } from '@/components/Pages/ChartItemSetting'
import dataJson from './data.json'
import { width } from 'dom-helpers'

const props = defineProps({
  optionData: {
    type: Object as PropType<GlobalThemeJsonType>,
    required: true
  },
  chartAttr: {
    type: Object as PropType<Omit<PickCreateComponentType<'attr'>, 'node' | 'conNode'>>,
    required: true
  }
})

// 表格配置选项
const selectTableOptions = [
  {
    label: '食堂物资数据',
    value: "CanteenMaterial",
    data: [],
    width: 673,
    height: 528,
    scrollHeight: 400,
    limiScrollNum: 10,
    dateType: 'week',
    dateTypeOptions: ['week', 'month'],
    apiConfig: {
      apiFunction: 'apiBackgroundFundSupervisionBigShieldDrpInventoryRecord',
      apiParams: ['org_id', 'date_type']
    },
    tableSetting: [{
      label: '日期',
      key: 'date',
      type: 'text',
      flex: 3
    }, {
      label: '仓库名称',
      key: 'warehouse__name',
      type: 'text',
      flex: 4
    }, {
      label: '采购金额',
      key: 'total_purchase_fee',
      type: 'price',
      flex: 2
    }, {
      label: '入库金额',
      key: 'total_entry_fee',
      type: 'price',
      flex: 2
    }, {
      label: '出库金额',
      key: 'total_exit_fee',
      type: 'price',
      flex: 2
    }]
  },
  {
    label: '食材之都',
    value: "FoodCapital",
    data: [],
    width: 596,
    height: 330,
    scrollHeight: 200,
    limiScrollNum: 5,
    apiConfig: {
      apiFunction: 'apiBackgroundFundSupervisionBigShieldDrpMaterialsData',
      apiParams: ['org_id']
    },
    tableSetting: [{
      label: '供应商名称',
      key: 'supplier_manage_name',
      type: 'text',
      flex: 1
    }, {
      label: '物资名称',
      key: 'materials_name',
      type: 'text',
      flex: 1
    }]
  },
  {
    label: '食堂就餐消费金额',
    value: "CafeteriaDiningConsumptionAmount",
    data: [],
    width: 596,
    height: 328,
    scrollHeight: 156,
    limiScrollNum: 4,
    dateType: 'week',
    dateTypeOptions: ['week', 'month'],
    totalConfig: {
      title: '消费金额合计'
    },
    apiConfig: {
      apiFunction: 'apiBackgroundFundSupervisionBigShieldDietRepastConsumeAmount',
      apiParams: ['org_id', 'date_type']
    },
    tableSetting: [{
      label: '序号',
      key: 'no',
      type: 'index',
      flex: 1
    }, {
      label: '日期',
      key: 'date',
      type: 'text',
      flex: 2
    }, {
      label: '消费金额',
      key: 'price',
      type: 'price',
      flex: 2
    }]
  },
  {
    label: '原材料采购明细-本月',
    value: "RawMaterialPurchaseDetailsCurrentMonth",
    data: [],
    width: 596,
    height: 450,
    scrollHeight: 380,
    limiScrollNum: 4,
    dateType: 'month',
    dateTypeOptions: [],
    apiConfig: {
      apiFunction: 'apiBackgroundFundSupervisionBigShieldDietRawMaterialsPercentageRank',
      apiParams: ['org_id', 'date_type']
    },
    tableSetting: [{
      label: '序号',
      key: 'no',
      type: 'index',
      flex: 1
    }, {
      label: '采购日期',
      key: 'data_str',
      type: 'text',
      flex: 2
    }, {
      label: '采购金额',
      key: 'price',
      type: 'price',
      flex: 2
    }, {
      label: '所属仓库',
      key: 'warehouse_name',
      type: 'text',
      flex: 2
    }]
  }
]

const selectTableValueHandle = (value: any) => {
  const selectedOption = selectTableOptions.find(item => item.value === value)
  if (selectedOption) {
    props.optionData.titleValue = selectedOption.label
    props.optionData.tableDataType = selectedOption.value
    props.optionData.tableConfig = selectedOption
    props.chartAttr.w = selectedOption.width ? selectedOption.width : 673
    props.chartAttr.h = selectedOption.height ? selectedOption.height : 528
  }
}

// 初始化时设置默认的 tableSetting
const initTableSetting = () => {
  const currentType = props.optionData.tableDataType
  if (currentType) {
    selectTableValueHandle(currentType)
  }
}

// 组件挂载时初始化
initTableSetting()

</script>
<style scoped lang="scss">
 .input-tag{
  width: 200px;
 }
</style>