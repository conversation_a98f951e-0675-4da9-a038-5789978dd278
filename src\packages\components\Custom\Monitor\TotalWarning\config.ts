import { PublicConfigClass } from '@/packages/public'
import { TotalWarning } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import { chartInitConfig } from '@/settings/designSetting'
import dData from './data.json'

export const option = {
  titleValue: "", // 标题
  dataType: '', // 数据类型
  dataList: cloneDeep(dData.dataList) , // 数据列表
  selectValue: '' as string | number, // 选择值
  organId: 0 as string | number, // 组织id
}

export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = TotalWarning.key
  public chartConfig = cloneDeep(TotalWarning)
  public attr = {...chartInitConfig, w:750, h:90, zIndex:-1}
  // 图表配置项
  public option = cloneDeep(option)
}
