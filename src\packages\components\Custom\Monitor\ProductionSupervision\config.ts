import { PublicConfigClass } from '@/packages/public'
import { ProductionSupervision } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import { chartInitConfig } from '@/settings/designSetting'

export const option = {
  titleValue: "生产监督", // 标题
  type: 'video', // 类型
  videoList: [] as any[] , // 视频列表
  dataType: '', // 数据类型
  dataList: [] , // 数据列表
  selectValue: '' as string | number, // 选择值
  organId: 0 as string | number, // 组织id,
  isScroll: true
}

export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = ProductionSupervision.key
  public chartConfig = cloneDeep(ProductionSupervision)
  public attr = {...chartInitConfig, w:365, h:198, zIndex:-1}
  // 图表配置项
  public option = cloneDeep(option)
}
