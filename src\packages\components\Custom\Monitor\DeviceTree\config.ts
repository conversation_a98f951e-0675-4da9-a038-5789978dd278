import { PublicConfigClass } from '@/packages/public'
import { DeviceTree } from './index'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import { chartInitConfig } from '@/settings/designSetting'

export const option = {
  titleValue: "设备树", // 标题
  type: '', // 类型
  dataType: '', // 数据类型
  dataList: [] , // 数据列表
  selectValue: '' as string | number, // 选择值
  organId: 0 as string | number, // 组织id
}

export default class Config extends PublicConfigClass implements CreateComponentType {
  public key = DeviceTree.key
  public chartConfig = cloneDeep(DeviceTree)
  public attr = {...chartInitConfig, w:365, h:198, zIndex:-1}
  // 图表配置项
  public option = cloneDeep(option)
}
